<!--
==================== 用户中心主页组件 ====================
主要功能：
1. 展示用户订单状态导航，支持不同业务线的订单状态配置
2. 显示各订单状态的数量统计，实时更新订单计数
3. 提供快捷功能入口（地址管理、售后服务、帮助中心等）
4. 支持企业信息展示（针对ZQ业务线）
5. 响应式布局适配不同屏幕尺寸

技术特性：
- 使用 shallowRef 优化大数据性能
- 节流处理用户点击事件
- 条件渲染优化页面加载
- 缓存计算结果减少重复运算
- 支持多业务线配置差异化

使用场景：
- 用户查看订单状态和数量
- 快速访问常用功能
- 企业用户查看企业信息
-->
<template>
  <MainLayout>
    <div class="user-page">
      <!-- 企业信息展示区域 - 仅ZQ业务线显示 -->
      <div class="enterprise-info" v-if="bizCode === 'zq'">
        <img class="enterprise-icon" src="./assets/ZQMall/enterprise-profile.png" alt="企业头像" />
        <div class="enterprise-text">{{enterpriseName}}</div>
      </div>

      <!-- 用户订单状态导航头部 -->
      <header class="user-page__header" :class="{ 'user-page__header--with-enterprise': bizCode === 'zq' }">
        <h2 class="user-page__title">我的订单</h2>
        <!-- 订单状态导航栏 - 支持不同业务线配置 -->
        <nav class="order-status" role="navigation" aria-label="订单状态导航">
          <button v-for="item in bannerList" :key="item.key" class="order-status__item"
            :class="{ 'order-status__item--separator': item.separator }"
            :aria-label="item.separator ? '分隔符' : `${item.name}订单`" @click="handleOrderClick(item.key)">
            <!-- 订单状态分隔符 -->
            <i v-if="item.separator" :class="['order-status__separator', item.icon]" aria-hidden="true" />
            <!-- 订单状态内容区域 -->
            <div v-else class="order-status__content">
              <!-- 订单数量徽章 - 显示对应状态的订单数量 -->
              <van-badge :content="orderCounts[item.key] || 0" :show-zero="false" :max="99" class="order-status__badge">
                <i
                  :class="['order-status__icon', item.icon]"
                  :style="{ backgroundImage: `url(${getIconPath(item.icon.replace('icon-', 'icon-status-'))})` }"
                  aria-hidden="true"
                />
              </van-badge>
              <!-- 订单状态文本标签 -->
              <span class="order-status__text">{{ item.name }}</span>
            </div>
          </button>
        </nav>
      </header>

      <!-- 页面分隔线 -->
      <div class="user-page__divider" />

      <!-- 用户功能链接区域 -->
      <section class="user-links">
        <button v-for="link in visibleLinks" :key="link.key" class="user-links__item" :aria-label="`进入${link.text}`"
          @click="link.handler">
          <span class="user-links__text">{{ link.text }}</span>
          <i class="user-links__arrow" aria-hidden="true" />
        </button>
      </section>
    </div>
  </MainLayout>
</template>

<script setup>
import { computed, onMounted, onUnmounted, shallowRef } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { throttle } from 'lodash-es'
import { getBizCode } from '@utils/curEnv.js'
import { useUserStore } from '@store/modules/user.js'
import { useOrderStore } from '@store/modules/order.js'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import { closeToast, showLoadingToast } from 'vant'
import { getEnterpriseManagerInfo } from '@utils/zqInfo.js'

// ==================== 路由和状态管理初始化 ====================
const router = useRouter() // 路由实例 - 用于页面跳转
const route = useRoute() // 当前路由信息 - 用于获取查询参数
const userStore = useUserStore() // 用户状态管理 - 处理登录状态
const orderStore = useOrderStore() // 订单状态管理 - 处理订单数据

// ==================== 业务配置区域 ====================
// 业务代码缓存 - 避免重复计算，提升性能
const bizCode = getBizCode()

// 心愿单功能显示判断 - 仅特定业务线支持
const isWishShow = computed(() => {
  const bizCode = getBizCode()
  switch (bizCode) {
    case 'ziying':
    case 'labor':
    case 'ygjd':
      return true
    default:
      return false
  }
})

// ==================== 图标路径处理 ====================
// 根据业务代码获取对应的图标路径
const getIconPath = (iconName) => {
  if (bizCode === 'zq') {
    return new URL(`./assets/ZQMall/${iconName}.png`, import.meta.url).href
  } else if (bizCode === 'ygjd') {
    return new URL(`./assets/JDMall/${iconName}.png`, import.meta.url).href
  } else {
    return new URL(`./assets/WoMall/${iconName}.png`, import.meta.url).href
  }
}

// ==================== 企业信息处理 ====================
// 企业名称计算属性 - 用于ZQ业务线显示企业信息
const enterpriseName = computed(() => {
  const enterpriseInfo = getEnterpriseManagerInfo()
  return enterpriseInfo ? enterpriseInfo.ciName : '-'
})

// ==================== 订单状态配置 ====================
// 根据业务代码获取订单状态配置 - 不同业务线有不同的订单状态
const getBannerList = () => {
  if (bizCode === 'zq') {
    // ZQ业务线订单状态配置
    return [
      { name: '待发货', icon: 'icon-dfh', key: '3' },
      { name: '待收货', icon: 'icon-dsh', key: '5' },
      { name: '已取消', icon: 'icon-yqx', key: '2' },
      { name: '已签收', icon: 'icon-ywc', key: '9' },
      { separator: true, icon: 'icon-separator', key: '999' },
      { name: '所有订单', icon: 'icon-sydd', key: '' }
    ]
  } else {
    // 其他业务线订单状态配置
    return [
      { name: '待付款', icon: 'icon-dfk', key: '0' },
      { name: '待发货', icon: 'icon-dfh', key: '3' },
      { name: '待收货', icon: 'icon-dsh', key: '5' },
      { name: '售后', icon: 'icon-sh', key: '-1' },
      { separator: true, icon: 'icon-separator', key: '999' },
      { name: '所有订单', icon: 'icon-sydd', key: '' }
    ]
  }
}

// 订单状态列表 - 使用shallowRef优化性能，避免深度响应
const bannerList = shallowRef(getBannerList())

// ==================== 订单数量统计 ====================
// 订单数量计算属性 - 实时获取各状态订单数量
const orderCounts = computed(() => {
  const counts = {}
  bannerList.value.forEach(item => {
    if (!item.separator) {
      counts[item.key] = orderStore.getCountByType(item.key)
    }
  })
  return counts
})

// ==================== 功能链接配置 ====================
// 用户功能链接配置 - 预计算显示状态，提升渲染性能
const linkConfigs = shallowRef([
  {
    key: 'addr',
    text: '地址管理',
    handler: () => router.push({ path: '/addr/list', query: { callback: route.path } }),
    show: true
  },
  {
    key: 'afterSale',
    text: '售后服务',
    handler: () => router.push({
      path: '/wo-after-sales-list',
      query: { type: '-1', _t: Date.now().toString() }
    }),
    show: true
  },
  {
    key: 'help',
    text: '帮助中心',
    handler: () => router.push('/user/help'),
    show: bizCode === 'fupin' // 仅扶贫业务线显示
  },
  {
    key: 'hotline',
    text: '在线客服',
    handler: () => {
      // 跳转到联通在线客服系统
      window.location.href = 'https://service.unicompayment.com/live800/chatClient/chatbox.jsp?companyID=9061&configID=47&pagereferrer=%e5%95%86%e5%9f%8e&chatfrom=sc&enterurl=sc&sc=sc'
    },
    show: true
  },
  {
    key: 'wish',
    text: '心愿单',
    handler: () => router.push({
      name: 'user-wish',
      query: { ...route.query, timestamp: Date.now() }
    }),
    show: isWishShow.value // 仅支持心愿单的业务线显示
  }
])

// 可见链接过滤 - 根据业务线和显示条件过滤可见链接
const visibleLinks = linkConfigs.value.filter(
  link => link.show && (bizCode !== 'zq' || link.key === 'addr')
)

// ==================== 事件处理函数 ====================
// 订单点击处理 - 使用节流防止重复点击
const handleOrderClick = throttle((type) => {
  if (type === '-1') {
    // 售后订单跳转
    router.push({
      path: '/wo-after-sales',
      query: { type, _t: Date.now().toString() }
    })
  } else {
    // 普通订单跳转
    router.push({
      path: '/user/order/list',
      query: { type, _t: Date.now().toString() }
    })
  }
}, 300)

// ==================== 数据初始化 ====================
// 检查登录状态并获取订单数据 - 优化加载策略
const checkLoginAndFetchData = async () => {
  try {
    // 查询用户登录状态
    await userStore.queryLoginStatus()
    // 仅在用户已登录且非ZQ业务线时获取订单数据
    if (userStore.isLogin && bizCode !== 'zq') {
      showLoadingToast() // 显示加载提示
      await orderStore.fetchOrderCountDebounced() // 防抖获取订单数量
      closeToast() // 关闭加载提示
    }
  } catch (error) {
    // 静默处理错误，避免影响用户体验
    // console.error('初始化数据失败:', error)
  }
}

// ==================== 生命周期钩子 ====================
// 组件挂载时初始化数据
onMounted(() => {
  checkLoginAndFetchData()
})

// 组件卸载时清理缓存
onUnmounted(() => {
  orderStore.clearCache()
})
</script>

<style lang="less" scoped>
.user-page {
  width: 100%;
  background: #F8F9FA;
  contain: layout style;
  min-height: 100vh;

  .enterprise-info {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background-color: #FFFFFF;
    margin-bottom: 0;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

    .enterprise-icon {
      width: 35px;
      height: 35px;
      margin-right: 10px;
      border-radius: 50%;
    }

    .enterprise-text {
      font-size: 15px;
      font-weight: 500;
      color: #333333;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-all;
    }
  }

  &__header {
    padding: 15px;
    background: #FFFFFF;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    contain: layout;
    position: relative;
    box-sizing: border-box;

    &--with-enterprise {
      border-radius: 0 0 16px 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
      border-top: 1px solid #E2E8EE;
    }
  }

  &__title {
    margin-bottom: 16px;
    text-align: left;
    font-size: 18px;
    font-weight: 700;
    color: #171E24;
    contain: style;
    letter-spacing: 0.5px;
  }

  &__divider {
    width: 100%;
    height: 12px;
    background: #F8F9FA;
    margin: 0;
    will-change: auto;
  }
}

.order-status {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: nowrap;
  text-align: center;
  contain: layout;
  gap: 8px;

  &__item {
    position: relative;
    width: 19%;
    border: none;
    background: transparent;
    padding: 12px 0;
    cursor: pointer;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    border-radius: 4px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &--separator {
      width: 5%;
      padding: 0;
    }

    // 悬停效果
    //&:hover:not(&--separator) {
    //  background: linear-gradient(135deg, rgba(255, 122, 10, 0.08) 0%, rgba(255, 122, 10, 0.04) 100%);
    //  transform: translateY(-2px);
    //  box-shadow: 0 4px 12px rgba(255, 122, 10, 0.15);
    //}

    // 添加点击反馈
    //&:active:not(&--separator) {
    //  transform: translateY(0) scale(0.96);
    //  transition: transform 0.1s ease;
    //  box-shadow: 0 2px 8px rgba(255, 122, 10, 0.2);
    //}
  }

  &__separator {
    display: block;
    margin: 0 auto;
    width: 2px;
    height: 40px;
    //background: linear-gradient(180deg, transparent 0%, #E2E8EE 20%, #E2E8EE 80%, transparent 100%);
    border-radius: 1px;

    &.icon-separator {
      background-image: none;
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  &__icon {
    display: block;
    margin: 0 auto;
    width: 36px;
    height: 36px;
    background-size: 100%;
    background-repeat: no-repeat;
    will-change: transform;
    transform: translateZ(0);
    border-radius: 8px;
    padding: 6px;
    background-color: rgba(255, 122, 10, 0.08);
    transition: all 0.2s ease;
  }

  &__badge {
    position: relative;
    display: inline-block;

    :deep(.van-badge) {
      background: var(--wo-biz-theme-color);

      .van-badge__wrapper {
        position: static;
      }

      //.van-badge__content {
      //  position: absolute;
      //  top: -8px;
      //  right: -8px;
      //  min-width: 18px;
      //  height: 18px;
      //  padding: 0 5px;
      //  background: linear-gradient(135deg, #EF4444 0%, darken(#EF4444, 10%) 100%);
      //  border: 2px solid #FFFFFF;
      //  border-radius: 9999px;
      //  font-size: 11px;
      //  line-height: 14px;
      //  color: #FFFFFF;
      //  text-align: center;
      //  box-shadow: 0 2px 8px rgba(255, 47, 47, 0.3);
      //  transform: scale(1);
      //  transform-origin: center;
      //  font-weight: 600;
      //}
    }
  }

  &__text {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 12px;
    color: #4A5568;
    font-weight: 500;
    letter-spacing: 0.2px;
  }
}

.user-links {
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-sizing: border-box;
  contain: layout; // 布局优化
  gap: 12px;

  &__item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 18px 20px;
    width: 100%;
    min-height: 60px;
    border: none;
    border-radius: 12px;
    background: #FFFFFF;
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 122, 10, 0.02) 0%, rgba(255, 122, 10, 0.01) 100%);
      opacity: 0;
      transition: opacity 0.2s ease;
    }
  }

  &__text {
    font-size: 16px;
    color: #171E24;
    contain: style;
    font-weight: 500;
    letter-spacing: 0.3px;
  }

  &__arrow {
    width: 20px;
    height: 20px;
    background-size: 100%;
    background-repeat: no-repeat;
    background-image: url(./assets/next-step.png);
    will-change: transform;
    transform: translateZ(0);
    transition: transform 0.2s ease;
    opacity: 0.6;
    filter: brightness(1.2);
  }
}
</style>
