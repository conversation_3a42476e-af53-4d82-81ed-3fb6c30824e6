<!--
/**
 * 智慧养老首页组件
 *
 * 主要功能：
 * 1. 展示智慧养老服务首页的完整布局，包括头部横幅、网格菜单和多层级内容展示
 * 2. 提供多层级横幅展示，包括主横幅、子横幅和第三层横幅，支持不同位置的营销活动
 * 3. 集成特殊商品推荐区域，采用横向滚动布局展示精选养老服务商品
 * 4. 提供商品分类头部导航，支持用户在不同商品类别间切换浏览
 * 5. 实现瀑布流商品展示，支持分页加载和无限滚动浏览
 * 6. 集成搜索功能和热区点击功能，提升用户交互体验
 * 7. 实现骨架屏加载效果和平滑过渡动画，优化用户体验
 *
 * 技术特点：
 * - 使用BaseHomeLayout作为基础布局组件，统一首页结构
 * - 采用多个插槽区域组织内容，支持灵活的布局扩展
 * - 集成多种骨架屏组件，提供细粒度的加载状态控制
 * - 使用transition组件实现骨架屏与内容的平滑切换
 * - 实现响应式设计，适配不同屏幕尺寸
 * - 集成状态管理，支持数据持久化和跨组件共享
 *
 * 使用场景：
 * - 智慧养老服务平台的主要入口页面
 * - 用户浏览和搜索养老服务商品的核心界面
 * - 展示养老服务推荐和分类导航的营销页面
 */
-->

<template>
  <!-- 智慧养老首页主容器 -->
  <!-- 使用BaseHomeLayout提供统一的首页布局结构 -->
  <!-- 配置搜索占位符、头部横幅、网格菜单等基础元素 -->
  <!-- 设置网格列数为5列，启用滚动显示模式适配更多菜单项 -->
  <BaseHomeLayout
    home-class="zy-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    grid-display-mode="scroll"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <!-- 附加内容区域插槽，用于展示多层级横幅和特殊商品 -->
    <template #additional-content>
      <!-- 子横幅展示区域 -->
      <!-- 当骨架屏状态为true或横幅数据有内容时显示 -->
      <!-- 使用transition组件实现骨架屏与内容的平滑切换 -->
      <div v-if="skeletonStates.subBanner || subBanner.length > 0" class="home-sub-banner-container">
        <transition name="skeleton-fade" mode="out-in">
          <!-- 子横幅骨架屏组件，在数据加载时显示 -->
          <SubBannerSkeleton v-if="skeletonStates.subBanner" key="sub-banner-skeleton" />
          <!-- 子横幅内容，点击时触发活动跳转处理 -->
          <div v-else-if="subBanner.length > 0" key="sub-banner-content" class="home-sub-banner">
            <img
              @click="handleActivityClick(subBanner[0], 'bottom')"
              :src="subBanner[0].imgUrl"
              alt=""
              class="home-sub-banner-img"
            />
          </div>
        </transition>
      </div>

      <!-- 第三层横幅展示区域 -->
      <!-- 当骨架屏状态为true或横幅数据有内容时显示 -->
      <!-- 采用左右分栏布局，支持展示两个并列的横幅内容 -->
      <div v-if="skeletonStates.thirdBanner || thirdBanner.length > 0" class="home-third-banner-container">
        <transition name="skeleton-fade" mode="out-in">
          <!-- 第三层横幅骨架屏组件，在数据加载时显示 -->
          <ThirdBannerSkeleton v-if="skeletonStates.thirdBanner" key="third-banner-skeleton" />
          <!-- 第三层横幅内容，左右分栏展示 -->
          <div v-else-if="thirdBanner.length > 0" key="third-banner-content" class="home-third-banner">
            <!-- 左侧横幅，点击时触发左侧活动跳转处理 -->
            <div
              v-if="thirdBanner[0]"
              @click="handleActivityClick(thirdBanner[0], 'left')"
              class="home-third-banner-left"
            >
              <img :src="thirdBanner[0].imgUrl" alt="" class="home-third-banner-img" />
            </div>
            <!-- 右侧横幅，点击时触发右侧活动跳转处理 -->
            <div
              v-if="thirdBanner[1]"
              @click="handleActivityClick(thirdBanner[1], 'right')"
              class="home-third-banner-right"
            >
              <img :src="thirdBanner[1].imgUrl" alt="" class="home-third-banner-img" />
            </div>
          </div>
        </transition>
      </div>

      <!-- 特殊商品推荐区域 -->
      <!-- 当骨架屏状态为true或商品列表有数据时显示 -->
      <!-- 采用横向滚动布局，支持背景图片和热区点击功能 -->
      <HorizontalScrollSection
        v-if="skeletonStates.specialGoods || specialGoodsList.length > 0"
        class="home-special-goods-container"
        :goods-list="specialGoodsList"
        :background-image="specialPoolIdImage"
        :show-hot-zone="true"
        card-type="mini"
        :skeleton-states="{ horizontal: skeletonStates.specialGoods }"
        @goods-click="handleGoodsClick"
        @hot-zone-click="handleHotZoneClick"
      />

      <!-- 商品分类头部导航区域 -->
      <!-- 当骨架屏状态为true或分类列表有数据时显示 -->
      <!-- 提供商品分类切换功能，支持用户在不同类别间导航 -->
      <div v-if="skeletonStates.goodsHeader || typeList.length > 0" class="home-goods-header-container">
        <transition name="skeleton-fade" mode="out-in">
          <!-- 商品头部骨架屏组件，在数据加载时显示 -->
          <GoodsHeaderSkeleton v-if="skeletonStates.goodsHeader" key="goods-header-skeleton" />
          <!-- 商品头部导航组件，提供分类切换功能 -->
          <GoodsHeader
            v-else
            :typeList="typeList"
            key="goods-header-content"
            @switchTabs="switchTabs"
          />
        </transition>
      </div>
    </template>

    <!-- 主要内容区域插槽，展示瀑布流商品列表 -->
    <template #main-content>
      <!-- 瀑布流商品展示组件 -->
      <!-- 支持分页加载、无限滚动和渲染完成回调 -->
      <WaterfallSection
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import HorizontalScrollSection from '@views/Home/components/HorizontalScrollSection.vue'
import GoodsHeader from '@views/Home/components/GoodsHeader.vue'
import GoodsHeaderSkeleton from '@views/Home/components/Skeleton/GoodsHeaderSkeleton.vue'
import SubBannerSkeleton from '@views/Home/components/Skeleton/SubBannerSkeleton.vue'
import ThirdBannerSkeleton from '@views/Home/components/Skeleton/ThirdBannerSkeleton.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { closeToast, showLoadingToast } from 'vant'

// ==================== 首页基础数据和瀑布流功能 ====================
// 使用首页数据组合式函数，获取基础数据、瀑布流状态和相关工具函数
const {
  headerBannerList,        // 头部横幅列表数据
  gridMenuItems,           // 网格菜单项数据
  skeletonStates,          // 骨架屏显示状态控制
  moduleDataReady,         // 模块数据就绪状态，用于控制骨架屏隐藏时机
  waterfallGoodsList,      // 瀑布流商品列表数据
  waterfallLoading,        // 瀑布流加载状态
  waterfallFinished,       // 瀑布流加载完成状态
  waterfallButtonCanShow,  // 瀑布流加载更多按钮显示状态
  waterfallRenderComplete, // 瀑布流渲染完成状态
  waterfallCurrentPage,    // 瀑布流当前页码
  channelFilterd,          // 渠道过滤函数，根据当前渠道过滤数据
  transformGoodsData,      // 商品数据转换函数，统一商品数据格式
  getWaterfallList,        // 获取瀑布流商品列表的函数
  resetWaterfallState,     // 重置瀑布流状态的函数
  getPartionListData,      // 获取商品分区列表数据的函数
  hideSkeletonInOrder      // 按顺序隐藏骨架屏的函数，提供平滑的加载体验
} = useHomeData()

// ==================== 首页导航和交互功能 ====================
// 使用首页导航组合式函数，获取各种用户交互事件的处理函数
const {
  handleGoodsClick,        // 商品点击事件处理函数，跳转到商品详情页
  handleBannerClick,       // 横幅点击事件处理函数，处理横幅跳转逻辑
  handleGridItemClick,     // 网格菜单项点击事件处理函数，处理菜单导航
  handleMoreClick,         // 更多按钮点击事件处理函数，展开更多菜单
  handleSearch,            // 搜索事件处理函数，处理搜索跳转和参数传递
  handleActivityClick      // 活动点击事件处理函数，处理营销活动跳转
} = useHomeNavigation()

// ==================== 智慧养老页面特有数据管理 ====================
// 子横幅数据，用于展示第二层级的营销横幅
const subBanner = ref([])
// 第三层横幅数据，用于展示左右分栏的营销横幅
const thirdBanner = ref([])
// 商品分类列表数据，用于商品头部导航切换
const typeList = ref([])
// 当前选中的商品池ID，用于瀑布流商品数据获取
const goodsPoolIdSelected = ref('')
// 特殊商品列表数据，用于横向滚动推荐区域展示
const specialGoodsList = ref([])
// 特殊商品池ID，用于获取推荐商品数据
const specialPoolIdSelected = ref('')
// 特殊商品区域背景图片URL
const specialPoolIdImage = ref('')

// ==================== 骨架屏状态扩展配置 ====================
// 扩展骨架屏状态配置，为智慧养老页面特有的区域添加加载状态控制
skeletonStates.value = {
  ...skeletonStates.value,  // 保留原有的骨架屏状态配置
  subBanner: true,          // 子横幅区域骨架屏状态
  thirdBanner: true,        // 第三层横幅区域骨架屏状态
  specialGoods: true,       // 特殊商品推荐区域骨架屏状态
  goodsHeader: true         // 商品头部导航区域骨架屏状态
}

// ==================== 模块数据就绪状态配置 ====================
// 扩展模块数据就绪状态配置，用于控制各个区域骨架屏的隐藏时机
// 确保数据加载完成后才隐藏对应的骨架屏，提供更好的用户体验
moduleDataReady.value = {
  ...moduleDataReady.value, // 保留原有的模块数据就绪状态
  subBanner: false,         // 子横幅数据就绪状态
  thirdBanner: false,       // 第三层横幅数据就绪状态
  specialGoods: false,      // 特殊商品数据就绪状态
  goodsHeader: false        // 商品头部数据就绪状态
}

// ==================== 瀑布流商品交互处理 ====================
// 瀑布流渲染完成后的回调处理函数
// 当瀑布流组件完成渲染后调用，用于更新渲染完成状态
const handleWaterfallAfterRender = () => {
  // 标记瀑布流渲染已完成，可用于后续的性能优化或状态控制
  waterfallRenderComplete.value = true
}

// 瀑布流加载更多商品的事件处理函数
// 当用户触发加载更多操作时调用，获取下一页商品数据
const handleWaterfallLoadMore = () => {
  // 调用瀑布流数据获取函数，传入当前选中的商品池ID
  // 第二个参数为排序类型（空字符串表示默认排序）
  // 第三个参数为true表示这是加载更多操作
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// ==================== 特殊商品区域交互处理 ====================
// 特殊商品区域热区点击事件处理函数
// 当用户点击特殊商品推荐区域的热区时触发
const handleHotZoneClick = () => {
  // 热区点击逻辑，可用于跳转到特定页面或触发特定操作
  console.log('热区被点击')
}

// ==================== 横幅配置信息加载 ====================
// 加载所有横幅配置信息的异步函数
// 包括主横幅、子横幅、第三层横幅和背景图片的获取
const loadBannerConfigInfo = async () => {
  // 显示全局加载提示，告知用户正在加载数据
  showLoadingToast()

  // 获取主横幅数据（showPage: 1）
  // 用于首页顶部的轮播横幅展示
  const [, json] = await getBannerInfo({ bizCode: getBizCode(), showPage: 1 })
  // 对横幅数据进行渠道过滤和排序处理，按orderBy字段降序排列
  // 转换为统一的横幅数据格式，包含类型、URL、描述和跳转链接
  headerBannerList.value = channelFilterd(json.sort((a, b) => b.orderBy - a.orderBy) || []).map(item => ({
    type: 'image',              // 横幅类型，固定为图片类型
    url: item.imgUrl,           // 横幅图片URL
    alt: item.bannerChName,     // 横幅图片描述文本
    linkUrl: item.url,          // 横幅点击跳转链接
  }))
  // 标记主横幅数据已就绪
  moduleDataReady.value.banner = true

  // 获取子横幅数据（showPage: 2）
  // 用于主横幅下方的第二层营销横幅展示
  const [, json1] = await getBannerInfo({ bizCode: getBizCode(), showPage: 2 })
  // 对子横幅数据进行渠道过滤，确保只显示当前渠道相关的横幅
  subBanner.value = channelFilterd(json1 || [])
  // 标记子横幅数据已就绪
  moduleDataReady.value.subBanner = true

  // 获取第三层横幅数据（showPage: 3）
  // 用于左右分栏展示的第三层营销横幅
  const [, json2] = await getBannerInfo({ bizCode: getBizCode(), showPage: 3 })
  // 对第三层横幅数据进行渠道过滤
  thirdBanner.value = channelFilterd(json2 || [])
  // 标记第三层横幅数据已就绪
  moduleDataReady.value.thirdBanner = true

  // 获取背景图片数据（showPage: 4）
  // 用于特殊商品推荐区域的背景图片展示
  const [, json3] = await getBannerInfo({ bizCode: getBizCode(), showPage: 4 })
  const backImgList = json3 || []
  // 如果有背景图片数据，取第一张作为特殊商品区域的背景
  if (backImgList.length > 0) {
    specialPoolIdImage.value = backImgList[0].imgUrl
  }

  // 关闭全局加载提示
  closeToast()
  // 按指定顺序隐藏骨架屏，提供平滑的加载体验
  // 依次隐藏：主横幅 -> 网格菜单 -> 子横幅 -> 第三层横幅
  await hideSkeletonInOrder(['banner', 'gridMenu', 'subBanner', 'thirdBanner'])
}

// ==================== 网格菜单图标数据获取 ====================
// 获取网格菜单图标列表数据的异步函数
// 用于首页网格菜单区域的功能入口展示
const getIconList = async () => {
  // 调用图标信息API获取菜单图标数据
  // 使用查询业务代码和当前渠道信息进行数据过滤
  const [err, json] = await getIconInfo({
    bizCode: getBizCode('QUERY'),    // 查询业务代码，用于获取图标数据
    channel: curChannelBiz.get(),    // 当前渠道业务标识，确保获取对应渠道的图标
    showPage: 1                      // 显示页面标识，固定为1表示首页图标
  })

  // 处理API响应数据
  if (!err && json) {
    // 将原始图标数据转换为统一的菜单项格式
    // 兼容不同的字段命名，确保数据格式一致性
    const iconData = json.map(item => ({
      title: item.chName || item.title,                    // 图标标题，优先使用中文名称
      subtitle: item.iconSubTitle || item.subtitle,        // 图标副标题，用于补充说明
      icon: item.imgUrl || item.icon,                      // 图标图片URL
      url: item.url,                                       // 图标点击跳转链接
      badge: item.badge || item.iconBadge                  // 图标徽章，用于显示提示信息
    }))
    // 设置转换后的图标数据到网格菜单项
    gridMenuItems.value = iconData
  } else {
    // 请求失败时设置空数组，避免页面报错
    gridMenuItems.value = []
  }

  // 标记网格菜单数据已就绪
  moduleDataReady.value.gridMenu = true
  // 按指定顺序隐藏骨架屏，依次隐藏：主横幅 -> 网格菜单
  await hideSkeletonInOrder(['banner', 'gridMenu'])
}

// ==================== 特殊商品推荐数据获取 ====================
// 获取特殊商品推荐列表数据的异步函数
// 用于横向滚动推荐区域的商品展示
const getSpecialGoodsList = async () => {
  // 检查是否已选择特殊商品池ID，未选择则直接返回
  if (!specialPoolIdSelected.value) return

  // 构建特殊商品列表请求参数
  const params = {
    type: 'partion',                        // 请求类型为分区商品
    id: specialPoolIdSelected.value,        // 特殊商品池ID
    bizCode: getBizCode('GOODS'),           // 商品业务代码
    page_no: 1,                             // 固定获取第一页数据
    page_size: 20,                          // 固定获取20条数据，适合横向滚动展示
  }

  // 调用商品列表API获取特殊商品数据
  const [err, json] = await getGoodsList(params)
  if (!err && json) {
    // 使用数据转换函数统一商品数据格式
    specialGoodsList.value = json.map(transformGoodsData)
  }

  // 标记特殊商品数据已就绪
  moduleDataReady.value.specialGoods = true
  // 按指定顺序隐藏骨架屏，依次隐藏到特殊商品区域
  await hideSkeletonInOrder(['banner', 'gridMenu', 'subBanner', 'thirdBanner', 'specialGoods'])
}

// ==================== 商品分类切换处理 ====================
// 商品分类标签页切换事件处理函数
// 当用户点击商品头部导航的不同分类时触发
const switchTabs = async (id) => {
  // 更新当前选中的商品池ID，但不立即清空数据避免页面回弹
  goodsPoolIdSelected.value = id

  // 重置瀑布流分页状态，但保留当前显示的数据
  // 这样可以避免切换时的页面闪烁，提供更好的用户体验
  waterfallCurrentPage.value = 1          // 重置页码为第一页
  waterfallFinished.value = false         // 重置加载完成状态
  waterfallLoading.value = false          // 重置加载状态
  waterfallButtonCanShow.value = false    // 隐藏加载更多按钮
  waterfallRenderComplete.value = false   // 重置渲染完成状态

  // 等待DOM更新完成后再加载新数据
  // 确保状态重置已生效
  await nextTick()
  // 获取新分类的商品数据，不显示骨架屏直接替换数据
  getWaterfallList(id, '', false)
}

// ==================== 商品池切换处理 ====================
// 商品池切换处理函数
// 用于完全重置瀑布流状态并加载新的商品池数据
const changeGoodsPool = (id, sortType = '') => {
  // 更新当前选中的商品池ID
  goodsPoolIdSelected.value = id
  // 完全重置瀑布流状态，包括清空数据和重置所有状态
  resetWaterfallState()
  // 获取新商品池的数据，第三个参数false表示首次加载
  getWaterfallList(id, sortType, false)
}

// ==================== 页面初始化数据加载 ====================
// 页面初始化数据加载的异步函数
// 负责获取商品分类数据和初始化默认商品池
const initPage = async () => {
  // 获取主商品池分类数据（类型2表示主要商品分类）
  // 用于商品头部导航的分类切换功能
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  // 标记商品头部数据已就绪
  moduleDataReady.value.goodsHeader = true
  // 按指定顺序隐藏骨架屏，依次隐藏到商品头部区域
  await hideSkeletonInOrder(['banner', 'gridMenu', 'subBanner', 'thirdBanner', 'specialGoods', 'goodsHeader'])

  // 如果有商品分类数据，默认选择第一个分类并加载其商品数据
  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]  // 获取推荐分类（第一个分类）
    goodsPoolIdSelected.value = recommond.id  // 设置为当前选中的商品池ID
    changeGoodsPool(recommond.id)  // 切换到该商品池并加载数据
  }

  // 获取特殊商品池分类数据（类型1表示特殊推荐商品）
  // 用于横向滚动推荐区域的商品展示
  const specialPartionList = await getPartionListData(1)
  if (specialPartionList.length > 0) {
    // 选择第一个特殊商品池作为推荐商品来源
    specialPoolIdSelected.value = specialPartionList[0].id
    // 获取特殊商品列表数据
    getSpecialGoodsList()
  }
}

// ==================== 组件生命周期管理 ====================
// 组件挂载完成后的初始化操作
// 按照页面展示顺序依次加载各个区域的数据
onMounted(() => {
  // 加载横幅配置信息，包括主横幅、子横幅、第三层横幅和背景图
  loadBannerConfigInfo()

  // 获取网格菜单图标数据
  getIconList()

  // 初始化页面数据，包括商品分类和特殊商品推荐
  initPage()
})

// 组件卸载时的清理操作
// 预留清理函数，可用于取消未完成的请求或清理定时器
onUnmounted(() => {
  // 清理工作预留位置
  // 可在此处添加取消请求、清理定时器等操作
})
</script>

<style scoped lang="less">
.zy-home {
  .home-sub-banner-container {
    margin: 8px 10px;
    box-sizing: border-box;
  }

  .home-sub-banner {
    position: relative;
    width: 100%;
    box-sizing: border-box;

    .home-sub-banner-img {
      display: block;
      width: 100%;
      border-radius: 12px;
      box-sizing: border-box;
    }
  }

  .home-third-banner-container {
    margin: 8px 12px 30px;
    box-sizing: border-box;
  }

  .home-third-banner {
    display: flex;
    height: 210px;
    gap: 8px;
    box-sizing: border-box;

    .home-third-banner-left {
      display: flex;
      width: 50%;
      box-sizing: border-box;
    }

    .home-third-banner-right {
      display: flex;
      width: 50%;
      box-sizing: border-box;
    }

    .home-third-banner-img {
      width: 100%;
      height: 210px;
      border-radius: 12px;
      object-fit: cover;
      box-sizing: border-box;
    }
  }

  .home-goods-header-container {
    margin: 10px 0;
    box-sizing: border-box;
  }

  .home-special-goods-container {
    :deep(.home-horizontal-scroll-wrapper) {
      padding: 10px;
      box-sizing: border-box;
    }
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
