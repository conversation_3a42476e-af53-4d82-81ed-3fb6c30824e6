/**
 * @fileoverview 首页数据管理组合式函数
 * @description 提供首页所需的各种数据管理功能，包括轮播图、图标菜单、商品瀑布流等数据的获取和状态管理
 *
 * @example
 * // 在Vue组件中使用
 * import { useHomeData } from '@/views/Home/composables/useHomeData'
 *
 * export default {
 *   setup() {
 *     const {
 *       headerBannerList,
 *       gridMenuItems,
 *       waterfallGoodsList,
 *       getHeaderBannerList,
 *       getIconList,
 *       getWaterfallList
 *     } = useHomeData()
 *
 *     // 获取首页数据
 *     getHeaderBannerList()
 *     getIconList()
 *     getWaterfallList('poolId')
 *
 *     return {
 *       headerBannerList,
 *       gridMenuItems,
 *       waterfallGoodsList
 *     }
 *   }
 * }
 */

import { ref, nextTick } from 'vue'
import { throttle, compact } from 'lodash-es'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList, getPartionList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import { closeToast, showLoadingToast } from 'vant'
import { queryZqInfo } from '@/utils/zqInfo'
import { zqQuerySimplified } from '@/api/interface/zq'

/**
 * 首页数据管理组合式函数
 * @description 管理首页的轮播图、图标菜单、商品瀑布流等数据状态和相关操作
 * @returns {Object} 返回首页数据管理相关的响应式数据和方法
 * @returns {Ref<Array>} returns.headerBannerList - 头部轮播图列表
 * @returns {Ref<Array>} returns.gridMenuItems - 图标菜单项列表
 * @returns {Ref<Array>} returns.waterfallGoodsList - 瀑布流商品列表
 * @returns {Ref<Object>} returns.skeletonStates - 骨架屏显示状态
 * @returns {Ref<Object>} returns.moduleDataReady - 模块数据就绪状态
 * @returns {Function} returns.getHeaderBannerList - 获取轮播图数据
 * @returns {Function} returns.getIconList - 获取图标菜单数据
 * @returns {Function} returns.getWaterfallList - 获取商品瀑布流数据
 * @returns {Function} returns.resetWaterfallState - 重置瀑布流状态
 *
 * @example
 * const {
 *   headerBannerList,
 *   gridMenuItems,
 *   waterfallGoodsList,
 *   getHeaderBannerList,
 *   getIconList,
 *   getWaterfallList
 * } = useHomeData()
 */
export function useHomeData() {
  // 通用数据状态
  const headerBannerList = ref([])
  const gridMenuItems = ref([])
  const skeletonStates = ref({
    banner: true,
    gridMenu: true,
    waterfall: true
  })

  const moduleDataReady = ref({
    banner: false,
    gridMenu: false,
    waterfall: false
  })

  // 瀑布流数据状态
  const waterfallGoodsList = ref([])
  const waterfallLoading = ref(false)
  const waterfallFinished = ref(false)
  const waterfallCurrentPage = ref(1)
  const waterfallPageSize = ref(10)
  const waterfallButtonCanShow = ref(false)
  const waterfallRenderComplete = ref(false)

  /**
   * 根据渠道类型过滤数据
   * @description 根据当前渠道（联通、沃支付、其他）过滤数据列表
   * @param {Array} list - 需要过滤的数据列表
   * @returns {Array} 过滤后的数据列表
   * @example
   * const filteredBanners = channelFilterd(bannerList)
   */
  const channelFilterd = (list) => {
    if (isUnicom) {
      return list.filter(item => item.channelType === '1')
    } else if (isWopay) {
      return list.filter(item => item.channelType === '0')
    } else {
      return list.filter(item => item.channelType === '2')
    }
  }

  /**
   * 转换普通商品数据格式
   * @description 将API返回的商品数据转换为前端统一的数据格式
   * @param {Object} item - 原始商品数据
   * @param {string} item.name - 商品名称
   * @param {Array} item.skuList - SKU列表
   * @param {string} item.listImageUrl - 商品图片URL
   * @returns {Object} 转换后的商品数据
   * @returns {string} returns.name - 商品名称
   * @returns {number} returns.price - 商品价格
   * @returns {number} returns.sales - 销量
   * @returns {string} returns.goodsId - 商品ID
   * @returns {string} returns.image - 商品图片
   * @returns {string} returns.spec - 商品规格
   * @example
   * const transformedGoods = transformGoodsData(rawGoodsItem)
   */
  const transformGoodsData = (item) => ({
    name: item.name || item.goodName,
    price: item.skuList[0].price || item.goodsPrice,
    sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
    goodsId: item.id || item.goodsId,
    image: item.listImageUrl || item.image,
    spec: compact([
      item.skuList?.[0]?.param,
      item.skuList?.[0]?.param1,
      item.skuList?.[0]?.param2,
      item.skuList?.[0]?.param3,
      item.skuList?.[0]?.param4
    ]).join(' ') || item.spec || item.goodsSpec,
  })

  /**
   * 转换政企商城商品数据格式
   * @description 将政企商城API返回的商品数据转换为前端统一的数据格式，包含价格区间信息
   * @param {Object} item - 原始政企商品数据
   * @param {string} item.name - 商品名称
   * @param {Array} item.skuList - SKU列表
   * @param {string} item.listImageUrl - 商品图片URL
   * @returns {Object} 转换后的政企商品数据
   * @returns {string} returns.name - 商品名称
   * @returns {number} returns.price - 商品价格
   * @returns {number} returns.sales - 销量
   * @returns {string} returns.goodsId - 商品ID
   * @returns {string} returns.image - 商品图片
   * @returns {string} returns.spec - 商品规格
   * @returns {string} returns.lowPrice - 最低价格
   * @returns {string} returns.highPrice - 最高价格
   * @example
   * const transformedZqGoods = transformZqGoodsData(rawZqGoodsItem)
   */
  const transformZqGoodsData = (item) => ({
    name: item.name || item.goodName,
    price: item.skuList?.[0]?.price != null ? item.skuList[0].price : item.goodsPrice,
    sales: item.skuList?.[0]?.realSaleVolume || item.salesCount || 0,
    goodsId: item.id || item.goodsId,
    image: item.listImageUrl || item.image,
    spec: compact([
      item.skuList?.[0]?.param,
      item.skuList?.[0]?.param1,
      item.skuList?.[0]?.param2,
      item.skuList?.[0]?.param3,
      item.skuList?.[0]?.param4
    ]).join(' ') || item.spec || item.goodsSpec,
    lowPrice: item.skuList?.[0]?.lowPrice != null ? item.skuList?.[0]?.lowPrice : '',
    highPrice: item.skuList?.[0]?.highPrice != null ? item.skuList?.[0]?.highPrice : '',
  })

  /**
   * 判断是否需要显示首次加载的loading提示
   * @description 根据是否为加载更多操作、是否为首次加载、骨架屏状态来判断是否显示loading
   * @param {boolean} isLoadMore - 是否为加载更多操作
   * @returns {boolean} 是否需要显示loading提示
   * @example
   * const showToast = shouldShowInitialToast(false)
   */
  const shouldShowInitialToast = (isLoadMore) => {
    const isFirstLoad = waterfallGoodsList.value.length === 0
    return !isLoadMore && isFirstLoad && skeletonStates.value.waterfall
  }

  /**
   * 构建政企商城查询参数
   * @description 根据政企商城的业务逻辑构建查询参数，支持可选的store参数
   * @param {string} id - 商品池ID
   * @param {string} [sortType=''] - 排序类型
   * @param {Object|null} [store=null] - 可选的store对象，用于获取选中的供应商和地区
   * @param {string} [store.selectedIsvId] - 选中的供应商ID
   * @param {string} [store.selectedAreaId] - 选中的地区ID
   * @returns {Object} 政企商城查询参数对象
   * @returns {string} returns.roleType - 角色类型
   * @returns {number} returns.type - 查询类型
   * @returns {string} returns.bizCode - 业务代码
   * @returns {string} returns.id - 商品池ID
   * @returns {string} returns.supplierCode - 供应商代码
   * @returns {string} returns.proStr - 省份代码
   * @returns {number} returns.pageNo - 页码
   * @returns {number} returns.pageSize - 每页数量
   * @example
   * const params = buildZqParams('poolId', 'asc', store)
   */
  const buildZqParams = (id, sortType = '', store = null) => {
    const bizCode = getBizCode('GOODS')
    const zqInfo = queryZqInfo()
    console.warn(12313,zqInfo)
    let finalId = id
    let supplierCode = zqInfo.roleType !== '4' ? (zqInfo.isvList?.[0]?.isvId || '') : ''
    let proStr = zqInfo.roleType !== '4' ? (zqInfo.provinceCode?.join(',') || '') : ''
    let type = 2

    if (zqInfo.roleType === '4') {
      // roleType 为 4 时，优先从 store 取值；若无 store，保持为空
      finalId = ''
      if (store) {
        // 直接从 store 获取选中的值
        supplierCode = store.selectedIsvId || ''
        proStr = store.selectedAreaId || ''
      }
      type = 3
    }

    const params = {
      roleType: zqInfo.roleType,
      type,
      bizCode,
      id: finalId,
      supplierCode,
      proStr,
      pageNo: waterfallCurrentPage.value,
      pageSize: waterfallPageSize.value,
    }

    if (sortType) params.price_sort = sortType
    return params
  }

  /**
   * 构建普通商城查询参数
   * @description 构建普通商城的商品查询参数
   * @param {string} id - 商品池ID
   * @param {string} [sortType=''] - 排序类型
   * @returns {Object} 普通商城查询参数对象
   * @returns {string} returns.type - 查询类型
   * @returns {string} returns.id - 商品池ID
   * @returns {string} returns.bizCode - 业务代码
   * @returns {number} returns.page_no - 页码
   * @returns {number} returns.page_size - 每页数量
   * @example
   * const params = buildCommonParams('poolId', 'desc')
   */
  const buildCommonParams = (id, sortType = '') => {
    const bizCode = getBizCode('GOODS')
    const params = {
      type: 'partion',
      id,
      bizCode,
      page_no: waterfallCurrentPage.value,
      page_size: waterfallPageSize.value,
    }
    if (sortType) params.price_sort = sortType
    return params
  }

  /**
   * 统一瀑布流加载实现
   * @description 根据业务代码（政企或普通商城）加载瀑布流商品数据，支持分页和加载更多
   * @param {Object} options - 加载选项
   * @param {string} options.id - 商品池ID
   * @param {string} [options.sortType=''] - 排序类型
   * @param {boolean} [options.isLoadMore=false] - 是否为加载更多操作
   * @param {Object|null} [options.store=null] - 可选的store对象，用于政企商城获取选中的供应商和地区
   * @returns {Promise<void>} 无返回值的Promise
   * @example
   * // 首次加载
   * await loadWaterfall({ id: 'poolId', sortType: 'asc' })
   *
   * // 加载更多
   * await loadWaterfall({ id: 'poolId', sortType: 'asc', isLoadMore: true })
   *
   * // 政企商城加载（带store）
   * await loadWaterfall({ id: 'poolId', sortType: 'asc', isLoadMore: false, store })
   */
  const loadWaterfall = async ({ id, sortType = '', isLoadMore = false, store = null }) => {
    if (waterfallLoading.value || (waterfallFinished.value && isLoadMore)) return

    waterfallLoading.value = true

    const showToast = shouldShowInitialToast(isLoadMore)
    if (showToast) {
      waterfallRenderComplete.value = false
      showLoadingToast()
    }

    const bizCode = getBizCode('GOODS')

    let err, json
    if (bizCode === 'zq') {
      const params = buildZqParams(id, sortType, store)
      ;[err, json] = await zqQuerySimplified(params)
    } else {
      const params = buildCommonParams(id, sortType)
      ;[err, json] = await getGoodsList(params)
    }

    if (showToast) closeToast()

    if (!err && json) {
      const newItems = bizCode === 'zq'
        ? (json.goodsList || []).map(transformZqGoodsData)
        : json.map(transformGoodsData)

      if (isLoadMore) {
        waterfallGoodsList.value = [...waterfallGoodsList.value, ...newItems]
        if (bizCode === 'zq') {
          // 政企商城根据 cacheType 决定是否继续翻页
          if (json.cacheType === '1') {
            waterfallCurrentPage.value++
          }
        } else {
          waterfallCurrentPage.value++
        }
      } else {
        // 平滑替换数据，避免页面跳动
        waterfallGoodsList.value = newItems
        waterfallCurrentPage.value = 2
        moduleDataReady.value.waterfall = true
        if (skeletonStates.value.waterfall) {
          skeletonStates.value.waterfall = false
        }
      }

      // 结束判断
      if (bizCode === 'zq') {
        waterfallFinished.value = json.cacheType === '1' ? false : true
      } else {
        waterfallFinished.value = newItems.length === 0
      }
      waterfallButtonCanShow.value = true
    } else {
      waterfallFinished.value = true
      if (!isLoadMore) {
        moduleDataReady.value.waterfall = true
        if (skeletonStates.value.waterfall) {
          skeletonStates.value.waterfall = false
        }
      }
    }

    waterfallLoading.value = false
  }

  /**
   * 控制骨架屏的显示与隐藏
   * @description 按模块顺序隐藏骨架屏，确保数据就绪后再隐藏对应的骨架屏
   * @param {Array<string>} [moduleOrder=['banner', 'gridMenu']] - 模块隐藏顺序
   * @returns {Promise<void>} 无返回值的Promise
   * @example
   * await hideSkeletonInOrder(['banner', 'gridMenu'])
   */
  const hideSkeletonInOrder = async (moduleOrder = ['banner', 'gridMenu']) => {
    for (const module of moduleOrder) {
      if (moduleDataReady.value[module] && skeletonStates.value[module]) {
        skeletonStates.value[module] = false
        await nextTick()
      }
    }
  }

  /**
   * 获取Banner数据
   * @description 根据业务代码和显示页面获取横幅广告数据，并进行渠道过滤
   * @param {number} [showPage=1] - 显示页面标识
   * @returns {Promise<void>} 无返回值的Promise
   * @example
   * await getHeaderBannerList(1)
   */
  const getHeaderBannerList = async (showPage = 1) => {
    const [err, json] = await getBannerInfo({
      bizCode: getBizCode('QUERY'),
      showPage
    })

    if (!err) {
      const bannerData = channelFilterd(json).map(item => ({
        type: 'image',
        url: item.imgUrl,
        alt: item.bannerChName,
        linkUrl: item.url,
      }))
      headerBannerList.value = bannerData
    }

    moduleDataReady.value.banner = true
    await hideSkeletonInOrder(['banner'])
  }

  /**
   * 获取图标菜单数据
   * @description 根据业务代码、渠道和显示页面获取图标菜单数据
   * @param {number} [showPage=2] - 显示页面标识
   * @returns {Promise<void>} 无返回值的Promise
   * @example
   * await getIconList(2)
   */
  const getIconList = async (showPage = 2) => {
    const [err, json] = await getIconInfo({
      bizCode: getBizCode('QUERY'),
      channel: curChannelBiz.get(),
      showPage
    })

    if (!err && json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData
    } else {
      gridMenuItems.value = []
    }

    moduleDataReady.value.gridMenu = true
    await hideSkeletonInOrder(['banner', 'gridMenu'])
  }

  /**
   * 获取瀑布流商品列表数据
   * @description 获取瀑布流商品数据的简化接口，不支持store参数
   * @param {string} id - 商品池ID
   * @param {string} [sortType=''] - 排序类型
   * @param {boolean} [isLoadMore=false] - 是否为加载更多操作
   * @returns {Promise<void>} 无返回值的Promise
   * @example
   * await getWaterfallList('poolId', 'asc', false)
   */
  const getWaterfallList = async (id, sortType = '', isLoadMore = false) => {
    await loadWaterfall({ id, sortType, isLoadMore })
  }

  /**
   * 获取瀑布流商品列表数据（带store）
   * @description 获取瀑布流商品数据，支持传入store参数用于政企商城
   * @param {string} id - 商品池ID
   * @param {string} [sortType=''] - 排序类型
   * @param {boolean} [isLoadMore=false] - 是否为加载更多操作
   * @param {Object|null} [store=null] - 可选的store对象
   * @returns {Promise<void>} 无返回值的Promise
   * @example
   * await getWaterfallListWithStore('poolId', 'asc', false, store)
   */
  const getWaterfallListWithStore = async (id, sortType = '', isLoadMore = false, store = null) => {
    await loadWaterfall({ id, sortType, isLoadMore, store })
  }

  /**
   * 处理瀑布流加载更多
   * @description 处理瀑布流的加载更多操作，会检查加载状态和是否还有更多数据
   * @param {string} id - 商品池ID
   * @param {string} [sortType=''] - 排序类型
   * @param {Object|null} [store=null] - 可选的store对象
   * @returns {Promise<void>} 无返回值的Promise
   * @example
   * await handleWaterfallLoadMore('poolId', 'asc', store)
   */
  const handleWaterfallLoadMore = async (id, sortType = '', store = null) => {
    if (waterfallLoading.value || waterfallFinished.value) return
    await loadWaterfall({ id, sortType, isLoadMore: true, store })
  }

  /**
   * 重置瀑布流状态
   * @description 重置瀑布流相关的所有状态到初始值
   * @returns {void}
   * @example
   * resetWaterfallState()
   */
  const resetWaterfallState = () => {
    waterfallGoodsList.value = []
    waterfallCurrentPage.value = 1
    waterfallFinished.value = false
    waterfallLoading.value = false
    waterfallButtonCanShow.value = false
    waterfallRenderComplete.value = false
  }

  /**
   * 获取分区列表数据
   * @description 根据业务代码和类型获取商品分区列表数据，按位置排序
   * @param {number} [type=2] - 分区类型
   * @returns {Promise<Array>} 返回排序后的分区列表数组
   * @example
   * const partionList = await getPartionListData(2)
   */
  const getPartionListData = async (type = 2) => {
    showLoadingToast()
    const [err, json] = await getPartionList({
      bizCode: getBizCode('GOODS'),
      type
    })
    closeToast()

    if (err) {
      return []
    }

    return json ? json.sort((a, b) => b.pos - a.pos) : []
  }

  return {
    // 数据状态
    headerBannerList,
    gridMenuItems,
    skeletonStates,
    moduleDataReady,
    waterfallGoodsList,
    waterfallLoading,
    waterfallFinished,
    waterfallCurrentPage,
    waterfallPageSize,
    waterfallButtonCanShow,
    waterfallRenderComplete,

    // 工具函数
    channelFilterd,
    transformGoodsData,
    hideSkeletonInOrder,

    // API 方法
    getHeaderBannerList,
    getIconList,
    getWaterfallList,
    getWaterfallListWithStore,
    handleWaterfallLoadMore,
    resetWaterfallState,
    getPartionListData
  }
}
