<!--
/**
 * 水平滚动区域组件
 *
 * 主要功能：
 * 1. 展示水平滚动的商品列表，支持左右滑动浏览
 * 2. 集成骨架屏加载效果，提供平滑的数据加载体验
 * 3. 支持热区功能，可配置热区点击事件
 * 4. 使用动态组件渲染商品卡片，支持不同卡片类型
 * 5. 支持自定义背景样式和响应式设计
 * 6. 实现商品点击事件处理和页面跳转
 *
 * 技术特点：
 * - 使用过渡动画实现骨架屏与内容的平滑切换
 * - 支持动态组件加载不同类型的商品卡片
 * - 集成热区功能和事件处理机制
 * - 使用计算属性和事件发射进行组件通信
 * - 支持自定义样式和背景配置
 *
 * 使用场景：
 * - 首页水平滚动商品推荐区域
 * - 分类页面的商品横向展示
 * - 任何需要水平滚动商品列表的场景
 */
-->

<template>
  <!-- 水平滚动区域主容器 -->
  <!-- 根据骨架屏状态或商品列表数据控制显示 -->
  <!-- 应用自定义背景样式 -->
  <div
    v-if="skeletonStates.horizontal || goodsList.length > 0"
    class="home-horizontal-scroll-container"
    :style="backgroundStyle"
  >
    <!-- 骨架屏与实际内容的过渡动画 -->
    <!-- 使用skeleton-fade过渡效果，提供平滑的加载体验 -->
    <transition name="skeleton-fade" mode="out-in">
      <!-- 水平滚动骨架屏，在数据加载时显示 -->
      <HorizontalScrollSkeleton v-if="skeletonStates.horizontal" :skeleton-count="5" key="horizontal-skeleton" />
      <!-- 水平滚动实际内容 -->
      <div v-else-if="goodsList.length > 0" key="horizontal-content" class="home-horizontal-scroll-content">
        <!-- 热区区域，可配置显示和点击事件 -->
        <div v-if="showHotZone" class="home-hot-zone" @click="handleHotZoneClick">
          <!-- 热区内容可以根据需要添加 -->
        </div>

        <!-- 水平滚动商品列表包装器 -->
        <div class="home-horizontal-scroll-wrapper">
          <!-- 商品项循环渲染 -->
          <!-- 使用商品ID作为唯一key，监听点击事件 -->
          <div
            class="home-goods-item"
            v-for="item in goodsList"
            :key="item.goodsId"
            @click="handleGoodsClick(item)"
          >
            <!-- 动态组件渲染商品卡片 -->
            <!-- 根据cardComponent属性选择不同的卡片组件 -->
            <!-- 传入商品信息并监听点击事件 -->
            <component
              :is="cardComponent"
              :goods-info="item"
              @click="handleGoodsClick(item)"
            />
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import HorizontalScrollSkeleton from '@views/Home/components/Skeleton/HorizontalScrollSkeleton.vue'
import ProductCard from '@views/Home/components/ProductCard.vue'
import ProductCardMini from '@views/Home/components/ProductCardMini.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 商品列表数据数组
  goodsList: {
    type: Array,
    default: () => []
  },
  // 背景图片URL，用于自定义区域背景
  backgroundImage: {
    type: String,
    default: ''
  },
  // 是否显示热区，控制热区的显示和隐藏
  showHotZone: {
    type: Boolean,
    default: false
  },
  // 商品卡片类型，支持普通卡片和迷你卡片
  cardType: {
    type: String,
    default: 'normal', // 'normal' | 'mini'
    validator: (value) => ['normal', 'mini'].includes(value)
  },
  // 骨架屏状态配置对象
  skeletonStates: {
    type: Object,
    default: () => ({
      horizontal: true    // 水平滚动区域骨架屏显示状态
    })
  }
})

// 定义组件向父组件发射的事件
const emit = defineEmits(['goods-click', 'hot-zone-click'])

// ==================== Props响应式解构 ====================
// 使用toRefs解构props，保持响应性
const { goodsList, backgroundImage, showHotZone, cardType, skeletonStates } = toRefs(props)

// ==================== 样式计算相关 ====================
// 计算属性：生成背景样式对象
// 根据backgroundImage属性动态生成CSS背景样式
const backgroundStyle = computed(() => {
  return backgroundImage.value
    ? {
        backgroundImage: `url(${backgroundImage.value})`,    // 背景图片URL
        backgroundSize: '100% 100%',                         // 背景图片尺寸
        backgroundPosition: 'center',                        // 背景图片位置
        backgroundRepeat: 'no-repeat'                        // 背景图片重复方式
      }
    : {}
})

// 计算属性：根据卡片类型选择对应的组件
// 支持普通商品卡片和迷你商品卡片两种类型
const cardComponent = computed(() => {
  return cardType.value === 'mini' ? ProductCardMini : ProductCard
})

// ==================== 事件处理方法 ====================
// 商品点击事件处理函数
// 接收商品信息并向父组件发射goods-click事件
const handleGoodsClick = (goodsInfo) => {
  emit('goods-click', goodsInfo)
}

// 热区点击事件处理函数
// 向父组件发射hot-zone-click事件
const handleHotZoneClick = () => {
  emit('hot-zone-click')
}
</script>

<style scoped lang="less">
.home-horizontal-scroll-container {
  position: relative;
  min-height: 180px;
  display: flex;
  align-items: center;
  border-radius: 12px;
  margin: 8px 12px;
  overflow: hidden;
  box-sizing: border-box;

  .home-horizontal-scroll-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;
  }

  .home-hot-zone {
    flex: 1;
    min-height: 40px;
    cursor: pointer;
    box-sizing: border-box;

    &:hover {
      opacity: 0.9;
    }
  }

  .home-horizontal-scroll-wrapper {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scroll-behavior: smooth;
    width: 100%;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };

    .home-goods-item {
      flex: 0 0 130px;
      cursor: pointer;
      box-sizing: border-box;

      &:last-child {
        margin-right: 12px;
      }
    }
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
