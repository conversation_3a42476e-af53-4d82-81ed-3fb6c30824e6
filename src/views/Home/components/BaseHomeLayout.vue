<!--
/**
 * 首页基础布局组件
 *
 * 主要功能：
 * 1. 提供首页的基础布局结构，包含搜索头部、轮播图、网格菜单等通用模块
 * 2. 支持模块化配置，可以灵活控制各个模块的显示和隐藏
 * 3. 集成骨架屏加载效果，提供平滑的数据加载体验
 * 4. 提供插槽机制，支持自定义额外内容和主要内容区域
 * 5. 实现事件代理机制，统一处理各模块的用户交互事件
 * 6. 支持响应式设计和自定义样式类名配置
 *
 * 技术特点：
 * - 使用插槽系统实现高度可扩展的布局结构
 * - 集成过渡动画和骨架屏，优化用户体验
 * - 支持props配置和事件发射，实现组件间通信
 * - 使用防抖处理搜索功能，避免频繁触发
 * - 采用模块化设计，支持按需显示功能模块
 *
 * 使用场景：
 * - 各种首页布局的基础组件
 * - 需要统一首页结构的电商平台
 * - 支持模块化配置的页面布局
 */
-->

<template>
  <!-- 首页布局主容器 -->
  <!-- 应用自定义样式类名，支持不同页面的个性化样式 -->
  <div class="home-layout" :class="homeClass">
    <!-- 搜索头部模块 -->
    <!-- 根据showModules.search配置控制显示 -->
    <!-- 绑定搜索关键词，配置占位符和跳转设置 -->
    <!-- 监听搜索事件并使用防抖处理 -->
    <SearchHeader
      v-if="showModules.search !== false"
      v-model="searchKeyword"
      :placeholder="searchPlaceholder"
      :redirect-to-search="true"
      redirect-url="/search"
      @search="handleSearch"
    />

    <!-- 轮播图模块容器 -->
    <!-- 根据showModules.banner配置和数据状态控制显示 -->
    <!-- 当骨架屏显示或有轮播图数据时显示容器 -->
    <div
      v-if="showModules.banner !== false && (skeletonStates.banner || headerBannerList.length > 0)"
      class="home-banner-container"
    >
      <!-- 骨架屏与轮播图的过渡动画 -->
      <!-- 使用skeleton-fade过渡效果，提供平滑切换 -->
      <transition name="skeleton-fade" mode="out-in">
        <!-- 轮播图骨架屏，在数据加载时显示 -->
        <BannerSkeleton v-if="skeletonStates.banner" key="banner-skeleton" />
        <!-- 商品轮播图组件 -->
        <!-- 配置横向模式、分页类型、自动播放、循环播放 -->
        <!-- 监听图片点击事件并向父组件发射 -->
        <GoodsSwiper
          v-else-if="headerBannerList.length > 0"
          key="banner-content"
          :imageList="headerBannerList"
          mode="landscape"
          paginationType="fraction"
          :autoplay="true"
          :loop="true"
          @image-click="handleBannerClick"
        />
      </transition>
    </div>

    <!-- 网格菜单模块容器 -->
    <!-- 根据showModules.gridMenu配置和数据状态控制显示 -->
    <!-- 当骨架屏显示或有菜单项数据时显示容器 -->
    <div
      v-if="showModules.gridMenu !== false && (skeletonStates.gridMenu || gridMenuItems.length > 0)"
      class="home-grid-menu-container"
    >
      <!-- 骨架屏与网格菜单的过渡动画 -->
      <!-- 使用skeleton-fade过渡效果，提供平滑切换 -->
      <transition name="skeleton-fade" mode="out-in">
        <!-- 网格菜单骨架屏，在数据加载时显示 -->
        <GridMenuSkeleton v-if="skeletonStates.gridMenu" key="grid-skeleton" />
        <!-- 图标网格组件 -->
        <!-- 传入菜单项数据、列数、显示模式等配置 -->
        <!-- 启用更多按钮，限制最大显示项数为10 -->
        <!-- 监听项点击和更多按钮点击事件 -->
        <IconGrid
          v-else-if="gridMenuItems.length > 0"
          key="grid-content"
          :items="gridMenuItems"
          :columns="gridColumns"
          :display-mode="gridDisplayMode"
          :show-more="true"
          :max-items="10"
          @item-click="handleGridItemClick"
          @more-click="handleMoreClick"
        />
      </transition>
    </div>

    <!-- 额外内容插槽 -->
    <!-- 允许父组件插入自定义的额外内容 -->
    <slot name="additional-content" />

    <!-- 主要内容插槽 -->
    <!-- 允许父组件插入主要的页面内容 -->
    <slot name="main-content" />
  </div>
</template>

<script setup>
import { ref, toRefs } from 'vue'
import { debounce } from 'lodash-es'
import SearchHeader from '@components/Common/SearchHeader.vue'
import IconGrid from '@views/Home/components/IconGrid.vue'
import BannerSkeleton from '@views/Home/components/Skeleton/BannerSkeleton.vue'
import GridMenuSkeleton from '@views/Home/components/Skeleton/GridMenuSkeleton.vue'
import GoodsSwiper from "@components/Common/GoodsSwiper.vue"

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数，包含布局配置、数据和状态
const props = defineProps({
  // 自定义样式类名，用于不同页面的个性化样式
  homeClass: {
    type: String,
    default: ''
  },
  // 搜索框占位符文本
  searchPlaceholder: {
    type: String,
    default: '搜索商品'
  },
  // 头部轮播图列表数据
  headerBannerList: {
    type: Array,
    default: () => []
  },
  // 网格菜单项数据列表
  gridMenuItems: {
    type: Array,
    default: () => []
  },
  // 网格菜单列数配置
  gridColumns: {
    type: Number,
    default: 5
  },
  // 网格菜单显示模式
  gridDisplayMode: {
    type: String,
    default: 'grid'
  },
  // 模块显示配置对象，控制各模块的显示状态
  showModules: {
    type: Object,
    default: () => ({
      search: true,      // 搜索模块显示状态
      banner: true,      // 轮播图模块显示状态
      gridMenu: true     // 网格菜单模块显示状态
    })
  },
  // 骨架屏状态配置对象，控制各模块骨架屏的显示
  skeletonStates: {
    type: Object,
    default: () => ({
      banner: true,      // 轮播图骨架屏显示状态
      gridMenu: true     // 网格菜单骨架屏显示状态
    })
  }
})

// 定义组件向父组件发射的事件
const emit = defineEmits(['search', 'banner-click', 'grid-item-click', 'more-click'])

// ==================== Props响应式解构 ====================
// 使用toRefs解构props，保持响应性
const {
  homeClass,           // 自定义样式类名
  searchPlaceholder,   // 搜索占位符
  headerBannerList,    // 轮播图列表
  gridMenuItems,       // 网格菜单项
  gridColumns,         // 网格列数
  gridDisplayMode,     // 网格显示模式
  showModules,         // 模块显示配置
  skeletonStates       // 骨架屏状态配置
} = toRefs(props)

// ==================== 搜索功能相关状态 ====================
// 搜索关键词状态，绑定到搜索输入框
const searchKeyword = ref('')

// ==================== 事件处理方法 ====================
// 搜索处理函数，使用防抖避免频繁触发搜索
// 延迟300ms执行，减少不必要的搜索请求
const handleSearch = debounce(() => {
  emit('search', searchKeyword.value)
}, 300)

// 轮播图点击处理函数
// 接收点击的轮播图项数据，向父组件发射banner-click事件
const handleBannerClick = ({ item }) => {
  emit('banner-click', { item })
}

// 网格菜单项点击处理函数
// 接收点击的菜单项数据，向父组件发射grid-item-click事件
const handleGridItemClick = ({ item }) => {
  emit('grid-item-click', { item })
}

// 更多按钮点击处理函数
// 向父组件发射more-click事件
const handleMoreClick = () => {
  emit('more-click')
}
</script>

<style scoped lang="less">
.home-layout {
  width: 100vw;
  height: 100%;
  overflow: auto;
  background: #F8F9FA;
  box-sizing: border-box;

  .home-banner-container {
    margin: 8px 0;
    padding: 0 10px;
    border-radius: 12px;
    overflow: hidden;
    box-sizing: border-box;
  }

  .home-grid-menu-container {
    border-radius: 12px;
    margin: 8px 12px;
    box-sizing: border-box;
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
