<!--
/**
 * 帮扶商城首页组件
 *
 * 主要功能：
 * 1. 展示帮扶商城首页的完整布局，包括头部横幅、网格菜单和商品展示区域
 * 2. 提供"各县销冠"商品展示，采用瀑布流布局支持分页加载更多功能
 * 3. 展示"新上好物"商品列表，采用横向滚动卡片形式呈现最新商品
 * 4. 提供"爆款好物"商品展示，采用瀑布流布局支持分页加载更多功能
 * 5. 集成搜索功能，支持用户搜索商品和服务
 * 6. 实现骨架屏加载效果，提升用户体验
 *
 * 技术特点：
 * - 使用BaseHomeLayout作为基础布局组件，统一首页结构
 * - 采用SectionContainer组件包装各个内容区块，保持布局一致性
 * - 集成WaterfallSection瀑布流组件，支持无限滚动加载
 * - 使用HorizontalScrollSection横向滚动组件展示推荐商品
 * - 实现响应式设计，适配不同屏幕尺寸
 * - 集成防抖处理，优化加载更多操作性能
 *
 * 使用场景：
 * - 帮扶商城平台的主要入口页面
 * - 用户浏览和搜索帮扶商城商品的核心界面
 * - 展示热门商品和新品推荐的营销页面
 */
-->

<template>
  <!-- 帮扶商城首页主容器 -->
  <!-- 使用BaseHomeLayout提供统一的首页布局结构 -->
  <!-- 配置搜索占位符、头部横幅、网格菜单等基础元素 -->
  <!-- 设置网格列数为5列，适配帮扶商城的菜单展示需求 -->
  <BaseHomeLayout
    home-class="bf-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <!-- 主要内容区域插槽 -->
    <template #main-content>
      <!-- 各县销冠商品展示区域 -->
      <!-- 当骨架屏状态为true或商品列表有数据时显示 -->
      <!-- 采用瀑布流布局，支持分页加载和无限滚动 -->
      <SectionContainer v-if="skeletonStates.limited || limitedList.length > 0" title="各县销冠">
        <WaterfallSection
          :waterfall-goods-list="limitedList"
          :waterfall-loading="limitedLoading"
          :waterfall-finished="limitedFinished"
          :waterfall-button-can-show="limitedButtonCanShow"
          :waterfall-render-complete="true"
          :skeleton-states="{ waterfall: skeletonStates.limited }"
          @goods-click="handleGoodsClick"
          @load-more="handleLimitedLoadMore"
        />
      </SectionContainer>

      <!-- 新上好物商品展示区域 -->
      <!-- 当骨架屏状态为true或商品列表有数据时显示 -->
      <!-- 采用横向滚动布局，使用mini卡片样式展示新品 -->
      <SectionContainer v-if="skeletonStates.newer || newerList.length > 0" title="新上好物">
        <HorizontalScrollSection
          card-type="mini"
          :goods-list="newerList"
          :skeleton-states="{ horizontal: skeletonStates.newer }"
          @goods-click="handleGoodsClick"
        />
      </SectionContainer>

      <!-- 爆款好物商品展示区域 -->
      <!-- 当骨架屏状态为true或商品列表有数据时显示 -->
      <!-- 采用瀑布流布局，支持分页加载和无限滚动 -->
      <SectionContainer v-if="skeletonStates.hotProducts || hotProductsList.length > 0" title="爆款好物">
        <WaterfallSection
          :waterfall-goods-list="hotProductsList"
          :waterfall-loading="hotProductsLoading"
          :waterfall-finished="hotProductsFinished"
          :waterfall-button-can-show="hotProductsButtonCanShow"
          :waterfall-render-complete="true"
          :skeleton-states="{ waterfall: skeletonStates.hotProducts }"
          @goods-click="handleGoodsClick"
          @load-more="handleHotProductsLoadMore"
        />
      </SectionContainer>
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { throttle } from 'lodash-es'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import SectionContainer from '@views/Home/components/SectionContainer.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import HorizontalScrollSection from '@views/Home/components/HorizontalScrollSection.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { closeToast, showLoadingToast } from 'vant'

// ==================== 首页基础数据和导航功能 ====================
// 使用首页数据组合式函数，获取头部横幅、网格菜单、骨架屏状态等基础数据
// 以及数据获取和转换相关的工具函数
const {
  headerBannerList,        // 头部横幅列表数据
  gridMenuItems,           // 网格菜单项数据
  skeletonStates,          // 骨架屏显示状态控制
  getHeaderBannerList,     // 获取头部横幅数据的函数
  getIconList,             // 获取图标列表数据的函数
  transformGoodsData       // 商品数据转换函数，统一商品数据格式
} = useHomeData()

// 使用首页导航组合式函数，获取各种用户交互事件的处理函数
// 包括商品点击、横幅点击、菜单点击、搜索等导航相关功能
const {
  handleGoodsClick,        // 商品点击事件处理函数，跳转到商品详情页
  handleBannerClick,       // 横幅点击事件处理函数，处理横幅跳转逻辑
  handleGridItemClick,     // 网格菜单项点击事件处理函数，处理菜单导航
  handleMoreClick,         // 更多按钮点击事件处理函数，展开更多菜单
  handleSearch             // 搜索事件处理函数，处理搜索跳转和参数传递
} = useHomeNavigation()

// ==================== 各县销冠商品数据管理 ====================
// 各县销冠商品列表数据，存储从服务器获取的商品信息
const limitedList = ref([])
// 各县销冠商品加载状态，控制加载动画显示
const limitedLoading = ref(false)
// 各县销冠商品加载完成状态，标识是否已加载完所有数据
const limitedFinished = ref(false)
// 各县销冠商品当前页码，用于分页加载控制
const limitedCurrentPage = ref(1)
// 各县销冠商品每页数量，控制每次请求的数据量
const limitedPageSize = ref(10)
// 各县销冠商品加载更多按钮显示状态，控制按钮是否可见
const limitedButtonCanShow = ref(false)

// ==================== 新上好物商品数据管理 ====================
// 新上好物商品列表数据，存储最新上架的商品信息
const newerList = ref([])
// 新上好物商品加载状态，控制加载动画显示
const newerLoading = ref(false)

// ==================== 爆款好物商品数据管理 ====================
// 爆款好物商品列表数据，存储热门推荐商品信息
const hotProductsList = ref([])
// 爆款好物商品加载状态，控制加载动画显示
const hotProductsLoading = ref(false)
// 爆款好物商品加载完成状态，标识是否已加载完所有数据
const hotProductsFinished = ref(false)
// 爆款好物商品当前页码，用于分页加载控制
const hotProductsCurrentPage = ref(1)
// 爆款好物商品每页数量，控制每次请求的数据量
const hotProductsPageSize = ref(10)
// 爆款好物商品加载更多按钮显示状态，控制按钮是否可见
const hotProductsButtonCanShow = ref(false)

// ==================== 骨架屏状态初始化 ====================
// 扩展骨架屏状态配置，为各个商品区域添加加载状态控制
// 初始状态设置为true，在数据加载完成后设置为false
skeletonStates.value = {
  ...skeletonStates.value,  // 保留原有的骨架屏状态配置
  limited: true,            // 各县销冠区域骨架屏状态
  newer: true,              // 新上好物区域骨架屏状态
  hotProducts: true         // 爆款好物区域骨架屏状态
}

// ==================== 各县销冠商品数据获取和加载 ====================
// 获取各县销冠商品列表数据的异步函数
// 支持首次加载和分页加载更多两种模式
const getLimitedList = async (isLoadMore = false) => {
  // 防止重复请求，如果正在加载中则直接返回
  if (limitedLoading.value) return

  // 设置加载状态为true，显示加载动画
  limitedLoading.value = true

  // 首次加载时显示全局加载提示，加载更多时不显示
  if (!isLoadMore) {
    showLoadingToast()
  }

  // 调用商品列表API获取各县销冠商品数据
  // 使用环境变量配置的商品ID，确保获取正确的商品分区数据
  const [err, json] = await getGoodsList({
    type: 'partion',                                                    // 请求类型为分区商品
    bizCode: getBizCode('GOODS'),                                       // 业务代码，标识商品业务
    page_no: limitedCurrentPage.value,                                  // 当前页码
    page_size: limitedPageSize.value,                                   // 每页数量
    id: import.meta.env.VITE_FP_HOME_PAGE_LIMITED_GOODS_ID             // 各县销冠商品分区ID
  })

  // 首次加载完成后关闭全局加载提示
  if (!isLoadMore) {
    closeToast()
  }

  // 处理API响应数据
  if (!err && json && Array.isArray(json)) {
    // 使用数据转换函数统一商品数据格式
    const newItems = json.map(transformGoodsData)

    if (isLoadMore) {
      // 加载更多模式：将新数据追加到现有列表末尾
      limitedList.value = [...limitedList.value, ...newItems]
      // 页码递增，为下次加载更多做准备
      limitedCurrentPage.value++
    } else {
      // 首次加载模式：直接替换列表数据
      limitedList.value = newItems
      // 关闭骨架屏显示
      skeletonStates.value.limited = false
      // 显示加载更多按钮
      limitedButtonCanShow.value = true
      // 设置下次加载的页码为2
      limitedCurrentPage.value = 2
    }

    // 根据返回数据判断是否已加载完所有数据
    limitedFinished.value = json.length === 0
  } else {
    // 请求失败或无数据时，标记为加载完成
    limitedFinished.value = true
    if (!isLoadMore) {
      // 首次加载失败时关闭骨架屏
      skeletonStates.value.limited = false
    }
  }

  // 重置加载状态
  limitedLoading.value = false
}

// 各县销冠商品加载更多事件处理函数
// 使用throttle防抖处理，避免用户快速点击导致的重复请求
// 300ms的防抖间隔确保用户体验流畅
const handleLimitedLoadMore = throttle(() => {
  // 检查是否还有更多数据且当前未在加载中
  if (!limitedFinished.value && !limitedLoading.value) {
    // 调用加载更多模式的数据获取函数
    getLimitedList(true)
  }
}, 300)

// ==================== 新上好物商品数据获取 ====================
// 获取新上好物商品列表数据的异步函数
// 新品列表只需要首次加载，不支持分页加载更多
const getNewerList = async () => {
  // 防止重复请求，如果正在加载中则直接返回
  if (newerLoading.value) return

  // 设置加载状态为true
  newerLoading.value = true

  // 调用商品列表API获取新上好物商品数据
  // 固定获取第一页的10条数据，用于横向滚动展示
  const [err, json] = await getGoodsList({
    type: 'partion',                                                    // 请求类型为分区商品
    bizCode: getBizCode('GOODS'),                                       // 业务代码，标识商品业务
    page_no: 1,                                                         // 固定获取第一页数据
    page_size: 10,                                                      // 固定获取10条数据
    id: import.meta.env.VITE_FP_HOME_PAGE_NEWER_GOODS_ID               // 新上好物商品分区ID
  })

  // 处理API响应数据
  if (!err && json && Array.isArray(json)) {
    // 使用数据转换函数统一商品数据格式，直接设置到新品列表
    newerList.value = json.map(transformGoodsData)
  }

  // 无论请求成功或失败，都关闭新品区域的骨架屏显示
  skeletonStates.value.newer = false
  // 重置加载状态
  newerLoading.value = false
}

// ==================== 爆款好物商品数据获取和加载 ====================
// 获取爆款好物商品列表数据的异步函数
// 支持首次加载和分页加载更多两种模式
const getHotProductsList = async (isLoadMore = false) => {
  // 防止重复请求，如果正在加载中则直接返回
  if (hotProductsLoading.value) return

  // 设置加载状态为true，显示加载动画
  hotProductsLoading.value = true

  // 加载更多时显示全局加载提示，首次加载时不显示（使用骨架屏）
  if (isLoadMore) {
    showLoadingToast()
  }

  // 调用商品列表API获取爆款好物商品数据
  // 使用环境变量配置的商品ID，确保获取正确的商品分区数据
  const [err, json] = await getGoodsList({
    type: 'partion',                                                    // 请求类型为分区商品
    bizCode: getBizCode('GOODS'),                                       // 业务代码，标识商品业务
    page_no: hotProductsCurrentPage.value,                             // 当前页码
    page_size: hotProductsPageSize.value,                              // 每页数量
    id: import.meta.env.VITE_FP_HOME_PAGE_GUESS_GOODS_ID              // 爆款好物商品分区ID
  })

  // 加载更多完成后关闭全局加载提示
  if (isLoadMore) {
    closeToast()
  }

  // 处理API响应数据
  if (!err && json && Array.isArray(json)) {
    // 使用数据转换函数统一商品数据格式
    const newItems = json.map(transformGoodsData)

    if (isLoadMore) {
      // 加载更多模式：将新数据追加到现有列表末尾
      hotProductsList.value = [...hotProductsList.value, ...newItems]
      // 页码递增，为下次加载更多做准备
      hotProductsCurrentPage.value++
    } else {
      // 首次加载模式：直接替换列表数据
      hotProductsList.value = newItems
      // 关闭骨架屏显示
      skeletonStates.value.hotProducts = false
      // 显示加载更多按钮
      hotProductsButtonCanShow.value = true
      // 设置下次加载的页码为2
      hotProductsCurrentPage.value = 2
    }

    // 根据返回数据判断是否已加载完所有数据
    hotProductsFinished.value = json.length === 0
  } else {
    // 请求失败或无数据时，标记为加载完成
    hotProductsFinished.value = true
    if (!isLoadMore) {
      // 首次加载失败时关闭骨架屏
      skeletonStates.value.hotProducts = false
    }
  }

  // 重置加载状态
  hotProductsLoading.value = false
}

// 爆款好物商品加载更多事件处理函数
// 使用throttle防抖处理，避免用户快速点击导致的重复请求
// 300ms的防抖间隔确保用户体验流畅
const handleHotProductsLoadMore = throttle(() => {
  // 检查是否还有更多数据且当前未在加载中
  if (!hotProductsFinished.value && !hotProductsLoading.value) {
    // 调用加载更多模式的数据获取函数
    getHotProductsList(true)
  }
}, 300)

// ==================== 组件生命周期管理 ====================
// 组件挂载完成后的初始化操作
// 按照页面展示顺序依次加载各个区域的数据
onMounted(() => {
  // 获取头部横幅数据，展示首页顶部轮播图
  getHeaderBannerList()

  // 获取网格菜单图标数据，展示首页功能入口
  getIconList()

  // 获取各县销冠商品数据，首次加载模式
  getLimitedList(false)

  // 获取新上好物商品数据
  getNewerList()

  // 获取爆款好物商品数据，首次加载模式
  getHotProductsList(false)
})
</script>

<style scoped lang="less">
.bf-home {
}
</style>
