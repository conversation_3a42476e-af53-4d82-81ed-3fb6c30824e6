<!--
/**
 * 智农商城介绍页面组件
 *
 * 主要功能：
 * 1. 展示智农商城（帮扶商城）的基本信息和介绍内容
 * 2. 提供轮播图展示功能，支持不同渠道的图片筛选
 * 3. 显示商城的详细描述和体验路径指引
 * 4. 提供供应商推荐入口，引导用户进行供应商推荐
 * 5. 支持骨架屏加载效果，提升用户体验
 * 6. 实现响应式设计，适配不同设备屏幕
 *
 * 技术特点：
 * - 使用GoodsSwiper组件实现轮播图功能
 * - 集成骨架屏和过渡动画效果
 * - 支持多渠道数据筛选（联通、沃钱包、其他）
 * - 使用memoize优化渠道筛选函数性能
 * - 实现数据转换和错误处理机制
 *
 * 使用场景：
 * - 数字乡村平台的商城介绍页面
 * - 供应商推荐功能的入口页面
 * - 帮扶商城的宣传和引导页面
 */
-->

<template>
  <!-- 智农商城主容器 -->
  <div class="zns-container">
    <!-- 主要内容卡片容器 -->
    <main class="zns-content-card">
      <!-- 轮播图区域头部 -->
      <!-- 根据shouldShowBanner计算属性控制显示 -->
      <header v-if="shouldShowBanner" class="zns-banner-section">
        <!-- 骨架屏与轮播图的过渡动画 -->
        <!-- 使用skeleton-fade过渡效果，提供平滑的加载体验 -->
        <Transition name="skeleton-fade" mode="out-in">
          <!-- 轮播图骨架屏，在数据加载时显示 -->
          <BannerSkeleton v-if="bannerLoading" key="banner-skeleton" />
          <!-- 商品轮播图组件 -->
          <!-- 配置横向模式、分页类型、自动播放、循环播放 -->
          <!-- 监听图片点击事件进行跳转处理 -->
          <GoodsSwiper
            v-else
            key="banner-content"
            :image-list="swiperList"
            mode="landscape"
            pagination-type="fraction"
            :autoplay="true"
            :loop="true"
            @image-click="handleBannerClick"
          />
        </Transition>
      </header>

      <!-- 商城描述信息区域 -->
      <section class="zns-description">
        <!-- 主要描述文本：商城基本信息介绍 -->
        <p class="zns-desc-main">
          帮扶商城是沃钱包的助农消费帮扶电商平台，于2019年12月上线，接入消费帮扶县47个，上架商品2500个，诚邀各供应商入驻。
        </p>
        <!-- 体验指引文本：告知用户如何访问商城 -->
        <p class="zns-desc-guide">
          体验路径：进入中国联通APP-我的-我的钱包-帮扶商城
        </p>
      </section>
    </main>

    <!-- 操作按钮区域 -->
    <div class="zns-action-wrapper">
      <!-- 推荐供应商按钮 -->
      <!-- 使用渐变样式，大尺寸，点击跳转到供应商推荐页面 -->
      <WoButton
        type="gradient"
        size="large"
        @click="handleRecommendClick"
      >
        推荐供应商
      </WoButton>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { isWopay, isUnicom } from 'commonkit'
import { showToast } from 'vant'
import { memoize } from 'lodash-es'
import GoodsSwiper from '@/components/Common/GoodsSwiper.vue'
import BannerSkeleton from '@views/Home/components/Skeleton/BannerSkeleton.vue'
import { getBannerList } from '@/api/interface/digitalVillage'
import { getBizCode } from '@/utils/curEnv'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'

// ==================== 路由管理 ====================
// 获取路由实例，用于页面跳转
const router = useRouter()

// ==================== 轮播图数据管理 ====================
// 轮播图列表数据
const swiperList = ref([])
// 轮播图加载状态，初始为true显示骨架屏
const bannerLoading = ref(true)

// 计算属性：判断是否应该显示轮播图区域
// 当正在加载或有轮播图数据时显示轮播图区域
const shouldShowBanner = computed(() =>
  bannerLoading.value || swiperList.value.length > 0
)

// ==================== 渠道筛选相关方法 ====================
// 使用memoize缓存渠道筛选函数，避免重复计算
// 根据不同渠道类型返回对应的筛选函数
const getChannelFilter = memoize(() => {
  if (isUnicom) {
    // 联通渠道：筛选channelType为'1'的数据
    return (item) => item.channelType === '1'
  } else if (isWopay) {
    // 沃钱包渠道：筛选channelType为'0'的数据
    return (item) => item.channelType === '0'
  } else {
    // 其他渠道：筛选channelType为'2'的数据
    return (item) => item.channelType === '2'
  }
})

// ==================== 数据转换相关方法 ====================
// 轮播图数据转换函数
// 将后端返回的轮播图数据转换为前端组件需要的格式
const transformBannerData = (bannerData) => {
  return bannerData.map((item, index) => ({
    id: item.id || index,                                    // 轮播图ID，如果没有则使用索引
    url: item.imgUrl,                                        // 图片URL
    alt: item.bannerChName || `轮播图 ${index + 1}`,         // 图片alt属性，用于无障碍访问
    title: item.bannerChName,                                // 图片标题
    linkUrl: item.url                                        // 点击跳转链接
  }))
}

// ==================== 用户交互处理 ====================
// 推荐供应商按钮点击处理函数
// 跳转到供应商推荐页面
const handleRecommendClick = () => {
  router.push('/digitalVillage/supplierRecommend')
}

// 轮播图点击处理函数
// 如果轮播图项有链接URL，则跳转到对应页面
const handleBannerClick = ({ item }) => {
  if (item?.linkUrl) {
    // 使用window.location.href进行页面跳转
    window.location.href = item.linkUrl
  }
}

// ==================== 数据加载相关方法 ====================
// 加载轮播图数据的异步函数
// 调用接口获取轮播图数据，进行渠道筛选和数据转换
const loadBannerData = async () => {
  // 构建请求参数
  const params = {
    bizCode: getBizCode('QUERY'),    // 查询业务代码
    showPage: '2'                    // 页面标识，'2'表示智农商城页面
  }

  try {
    // 调用获取轮播图列表接口
    const [err, json] = await getBannerList(params)

    if (err) {
      // 接口返回错误，记录错误信息并显示提示
      console.error('获取轮播图失败:', err.msg)
      showToast(err.msg)
      swiperList.value = []
      return
    }

    // 接口调用成功，处理返回数据
    const bannerData = json || []
    // 获取当前渠道的筛选函数
    const channelFilter = getChannelFilter()
    // 根据渠道类型筛选轮播图数据
    const filteredData = bannerData.filter(channelFilter)

    // 转换数据格式并更新轮播图列表
    swiperList.value = transformBannerData(filteredData)
  } catch (error) {
    // 接口调用异常，记录错误信息并清空列表
    console.error('获取轮播图异常:', error)
    swiperList.value = []
  } finally {
    // 无论成功或失败，都要关闭加载状态
    bannerLoading.value = false
  }
}

// ==================== 生命周期钩子 ====================
// 组件挂载时的初始化操作
// 加载轮播图数据
onMounted(() => {
  loadBannerData()
})
</script>

<style scoped lang="less">
.zns-container {
  padding: 17px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.zns-content-card {
  background: #ffffff;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.07);
  border-radius: 8px;
  overflow: hidden;
  flex: 1;
}

.zns-banner-section {
  position: relative;

  :deep(.goods-swiper) {
    border-radius: 8px 8px 0 0;
  }
}

.zns-description {
  padding: 20px;
}

.zns-desc-main {
  font-size: 14px;
  color: #5a6066;
  line-height: 1.6;
  font-weight: 400;
  margin: 0 0 20px 0;
  text-align: justify;
}

.zns-desc-guide {
  font-size: 13px;
  color: #5a6066;
  line-height: 1.5;
  font-weight: 400;
  margin: 0;
  text-align: justify;
}

.zns-action-wrapper {
  padding: 30px 0 20px;
  display: flex;
  justify-content: center;
}

.zns-recommend-btn {
  background: linear-gradient(135deg, #ffa033 0%, #ff6d33 100%);
  box-shadow: 0 5px 9px 0 rgba(255, 158, 51, 0.4);
  border-radius: 23px;
  border: none;
  height: 46px;
  min-width: 200px;
  font-size: 17px;
  color: #ffffff;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 7px 12px 0 rgba(255, 158, 51, 0.5);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 3px 6px 0 rgba(255, 158, 51, 0.4);
  }

  &:focus-visible {
    outline: 2px solid #ffa033;
    outline-offset: 2px;
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
