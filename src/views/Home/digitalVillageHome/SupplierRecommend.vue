<!--
/**
 * 供应商推荐管理组件
 *
 * 主要功能：
 * 1. 提供供应商推荐表单，支持用户推荐新的供应商信息
 * 2. 展示已推荐的供应商列表，包含推荐状态和详细信息
 * 3. 实现表单验证功能，确保推荐信息的完整性和准确性
 * 4. 支持推荐状态管理，区分"已推荐"和"已联系"两种状态
 * 5. 提供空状态展示，当无推荐记录时显示友好提示
 * 6. 集成防抖处理，避免重复提交和频繁操作
 *
 * 技术特点：
 * - 使用van-tabs实现标签页切换，支持动画和滑动效果
 * - 采用van-form进行表单管理和验证
 * - 实现响应式数据绑定和状态管理
 * - 支持异步数据加载和错误处理
 * - 集成加载状态和用户反馈机制
 *
 * 使用场景：
 * - 数字乡村平台的供应商推荐功能
 * - 用户推荐优质供应商的业务场景
 * - 供应商信息管理和状态跟踪
 */
-->

<template>
  <!-- 供应商推荐管理主容器 -->
  <div class="sr-container">
    <!-- 标签页容器，支持新增推荐和查看已推荐两个功能 -->
    <!-- 配置橙色主题色，支持动画和滑动切换 -->
    <!-- 监听标签页切换事件，触发相应的数据加载 -->
    <van-tabs
      v-model:active="activeTab"
      animated
      swipeable
      color="#FF780A"
      title-active-color="#FF780A"
      title-inactive-color="#171E24"
      line-width="50%"
      background="white"
      @change="handleTabChange"
    >
      <!-- 新增推荐标签页 -->
      <van-tab title="新增推荐">
        <!-- 新增推荐区域容器 -->
        <section class="sr-add-section">
          <!-- 表单容器，包含所有推荐信息输入字段 -->
          <div class="sr-form-wrapper">
            <!-- 推荐表单，使用ref获取表单实例进行验证 -->
            <van-form ref="formRef">
              <!-- 推荐人姓名输入字段 -->
              <!-- 绑定formData.recommendName，设置必填验证规则 -->
              <van-field
                v-model="formData.recommendName"
                name="推荐人姓名"
                label="推荐人姓名"
                placeholder="请输入姓名"
                label-width="40%"
                :rules="[{ required: true, message: '请输入推荐人姓名' }]"
              />
              <!-- 推荐人手机号输入字段 -->
              <!-- 绑定formData.recommendPhone，限制最大长度11位，使用手机号验证规则 -->
              <van-field
                v-model="formData.recommendPhone"
                name="推荐手机号"
                label="推荐手机号"
                maxlength="11"
                placeholder="请输入手机号码"
                label-width="40%"
                :rules="phoneRules"
              />
              <!-- 供应商名称输入字段 -->
              <!-- 绑定formData.supplierName，设置必填验证规则 -->
              <van-field
                v-model="formData.supplierName"
                name="推荐人供应商名称"
                label="推荐人供应商名称"
                placeholder="请输入供应商名称"
                label-width="40%"
                :rules="[{ required: true, message: '请输入供应商名称' }]"
              />
              <!-- 供应商联系人姓名输入字段 -->
              <!-- 绑定formData.supplierContactName，设置必填验证规则 -->
              <van-field
                v-model="formData.supplierContactName"
                name="推荐联系人姓名"
                label="推荐联系人姓名"
                placeholder="请输入姓名"
                label-width="40%"
                :rules="[{ required: true, message: '请输入联系人姓名' }]"
              />
              <!-- 供应商联系人手机号输入字段 -->
              <!-- 绑定formData.supplierContactPhone，限制最大长度11位，使用手机号验证规则 -->
              <van-field
                v-model="formData.supplierContactPhone"
                maxlength="11"
                name="推荐联系人手机号"
                label="推荐联系人手机号"
                placeholder="请输入手机号码"
                label-width="40%"
                :rules="phoneRules"
              />
            </van-form>
          </div>

          <!-- 表单底部区域，包含说明信息和操作按钮 -->
          <footer class="sr-form-footer">
            <!-- 联系说明信息 -->
            <div class="sr-notice">
              <p class="sr-notice-title">说明:</p>
              <p class="sr-notice-text">如有疑问请联系商务合作负责人员</p>
              <p class="sr-notice-contact">尚哲聪：18612261095</p>
            </div>
            <!-- 操作按钮组 -->
            <div class="sr-button-group">
              <!-- 取消按钮，点击返回上一页 -->
              <WoButton size="medium" @click="handleCancel">
                取消
              </WoButton>
              <!-- 提交按钮，显示加载状态，点击提交表单数据 -->
              <WoButton
                type="gradient"
                size="medium"
                :loading="submitLoading"
                @click="handleSubmit"
              >
                提交
              </WoButton>
            </div>
          </footer>
        </section>
      </van-tab>

      <!-- 已推荐标签页 -->
      <van-tab title="已推荐">
        <!-- 已推荐列表区域容器 -->
        <section class="sr-list-section">
          <!-- 有推荐记录时显示的列表容器 -->
          <div v-if="hasSupplierList" class="sr-list-wrapper">
            <!-- 推荐记录卡片，遍历supplierList数组 -->
            <!-- 使用item.id作为唯一key值 -->
            <article
              v-for="item in supplierList"
              :key="item.id"
              class="sr-item-card"
            >
              <!-- 推荐状态标签，根据状态显示不同样式和文字 -->
              <div :class="getStatusClass(item.status)">
                {{ getStatusText(item.status) }}
              </div>
              <!-- 推荐详情列表，使用dl/dt/dd语义化标签 -->
              <dl class="sr-item-details">
                <!-- 推荐供应商名称 -->
                <div class="sr-detail-row">
                  <dt>推荐供应商名称</dt>
                  <dd>{{ item.supplierName }}</dd>
                </div>
                <!-- 供应商联系人姓名 -->
                <div class="sr-detail-row">
                  <dt>供应商联系人姓名</dt>
                  <dd>{{ item.supplierContactName }}</dd>
                </div>
                <!-- 供应商联系人手机号 -->
                <div class="sr-detail-row">
                  <dt>供应商联系人手机号</dt>
                  <dd>{{ item.supplierContactPhone }}</dd>
                </div>
                <!-- 推荐人姓名 -->
                <div class="sr-detail-row">
                  <dt>推荐人姓名</dt>
                  <dd>{{ item.recommendName }}</dd>
                </div>
                <!-- 推荐人手机号 -->
                <div class="sr-detail-row">
                  <dt>推荐人手机号</dt>
                  <dd>{{ item.recommendPhone }}</dd>
                </div>
                <!-- 推荐日期 -->
                <div class="sr-detail-row">
                  <dt>推荐日期</dt>
                  <dd>{{ item.createTime }}</dd>
                </div>
              </dl>
            </article>

            <!-- 列表底部说明信息 -->
            <footer class="sr-list-footer">
              <span class="sr-footer-title">说明:</span>
              <span class="sr-footer-text">如有疑问请联系商务合作负责人员</span>
              <span class="sr-footer-contact">尚哲聪：18612261095</span>
            </footer>
          </div>

          <!-- 无推荐记录时显示的空状态 -->
          <div v-else class="sr-empty-state">
            <!-- 空状态图片 -->
            <img
              src="./assets/empty.png"
              alt="暂无数据"
              width="210"
              loading="lazy"
            />
            <!-- 空状态文字内容 -->
            <div class="sr-empty-content">
              <span class="sr-empty-title">暂无推荐记录</span>
              <span class="sr-empty-text">如有疑问请联系商务合作负责人员</span>
              <span class="sr-empty-contact">尚哲聪：18612261095</span>
            </div>
          </div>
        </section>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { debounce } from 'lodash-es'
import { isUnicom } from 'commonkit'
import { addSupplier, getSupplierList } from '@/api/interface/digitalVillage'
import { useRouter } from 'vue-router'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'

// ==================== 路由管理 ====================
// 获取路由实例，用于页面跳转和导航
const router = useRouter()

// ==================== 标签页和列表状态管理 ====================
// 当前激活的标签页索引，0为新增推荐，1为已推荐列表
const activeTab = ref(0)
// 供应商推荐列表数据
const supplierList = ref([])
// 表单提交加载状态，防止重复提交
const submitLoading = ref(false)
// 表单实例引用，用于表单验证
const formRef = ref()

// ==================== 表单数据管理 ====================
// 推荐表单数据，使用reactive创建响应式对象
const formData = reactive({
  supplierName: '',              // 供应商名称
  supplierContactName: '',       // 供应商联系人姓名
  supplierContactPhone: '',      // 供应商联系人手机号
  recommendName: '',             // 推荐人姓名
  recommendPhone: ''             // 推荐人手机号
})

// 计算属性：判断是否有供应商推荐列表数据
// 用于控制列表显示和空状态显示
const hasSupplierList = computed(() => supplierList.value.length > 0)

// ==================== 表单验证规则 ====================
// 手机号验证规则数组，包含必填和格式验证
const phoneRules = [
  { required: true, message: '请输入手机号码' },
  { pattern: /^1\d{10}$/, message: '请输入正确的手机号码' }
]

// ==================== 状态显示相关方法 ====================
// 根据推荐状态获取对应的CSS类名
// status为'1'时返回已推荐样式，否则返回已联系样式
const getStatusClass = (status) => {
  return status === '1' ? 'sr-status-recommended' : 'sr-status-contacted'
}

// 根据推荐状态获取对应的显示文本
// status为'1'时显示"已推荐"，否则显示"已联系"
const getStatusText = (status) => {
  return status === '1' ? '已推荐' : '已联系'
}

// ==================== 标签页切换处理 ====================
// 标签页切换事件处理函数
// 当切换到"已推荐"标签页时，加载供应商列表数据
const handleTabChange = (index) => {
  if (index === 1) {
    loadSupplierList()
  }
}

// ==================== 表单操作相关方法 ====================
// 重置表单数据，清空所有输入字段
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
}

// ==================== 表单提交处理 ====================
// 表单提交处理函数，使用防抖避免重复提交
// 执行表单验证，调用添加供应商接口，处理成功和失败情况
const handleSubmit = debounce(async () => {
  // 如果正在提交中，直接返回避免重复提交
  if (submitLoading.value) return

  try {
    // 执行表单验证，如果验证失败会抛出异常
    await formRef.value?.validate()
  } catch (error) {
    // 表单验证失败，直接返回不执行提交
    return
  }

  // 设置提交加载状态为true
  submitLoading.value = true

  try {
    // 调用添加供应商接口，传入表单数据
    const [err] = await addSupplier({ ...formData })

    if (!err) {
      // 添加成功，显示成功提示
      showToast('添加成功')
      // 重置表单数据
      resetForm()
      // 切换到已推荐标签页
      activeTab.value = 1
      // 等待DOM更新完成
      await nextTick()
      // 重新加载供应商列表数据
      await loadSupplierList()
    }
  } catch (error) {
    // 接口调用异常，记录错误信息
    console.error('提交失败:', error)
  } finally {
    // 无论成功或失败，都要重置提交加载状态
    submitLoading.value = false
  }
}, 300)

// ==================== 取消操作处理 ====================
// 取消按钮点击处理函数
// 返回上一页
const handleCancel = () => {
  router.back()
}

// ==================== 数据加载相关方法 ====================
// 加载供应商推荐列表数据
// 调用获取供应商列表接口，处理加载状态和错误情况
const loadSupplierList = async () => {
  // 非联通环境下显示加载提示
  if (!isUnicom) {
    showLoadingToast()
  }

  try {
    // 调用获取供应商列表接口
    const [err, json] = await getSupplierList({})
    if (!err) {
      // 接口调用成功，更新供应商列表数据
      supplierList.value = json || []
    }
  } catch (error) {
    // 接口调用异常，记录错误信息并清空列表
    console.error('获取供应商列表失败:', error)
    supplierList.value = []
  } finally {
    // 无论成功或失败，都要关闭加载提示
    if (!isUnicom) {
      closeToast()
    }
  }
}

// ==================== 生命周期钩子 ====================
// 组件挂载时的初始化操作
// 预加载供应商列表数据
onMounted(() => {
  loadSupplierList()
})

// 组件卸载时的清理操作
// 确保关闭所有可能存在的Toast提示
onUnmounted(() => {
  closeToast()
})
</script>
<style lang='less' scoped>
.sr-container {
  :deep(.van-tab) {
    font-size: 15px;
    line-height: 20px;
    cursor: pointer;
  }

  :deep(.van-cell) {
    color: #323233;
    font-size: 16px;
    padding: 16px 8px;
  }

  .sr-add-section {
    background-color: #F8F9FA;
    min-height: calc(100vh - 44px);
    display: flex;
    flex-direction: column;
  }

  .sr-form-wrapper {
    flex: 1;
    background-color: #FFFFFF;
  }

  .sr-form-footer {
    background-color: #FFFFFF;
    margin-top: 20px;
  }

  .sr-notice {
    padding: 17px;
    margin-bottom: 20px;

    .sr-notice-title {
      font-size: 13px;
      color: #4A5568;
      line-height: 1.5;
      font-weight: 400;
      margin: 0 0 8px 0;
    }

    .sr-notice-text,
    .sr-notice-contact {
      font-size: 13px;
      color: #4A5568;
      line-height: 21.5;
      font-weight: 400;
      margin: 0 0 4px 0;
    }
  }

  .sr-button-group {
    display: flex;
    justify-content: space-around;
    padding: 0 10px 20px;
    gap: 16px;

    :deep(.wo-button) {
      flex: 1;
      max-width: 120px;
    }
  }

  .sr-list-section {
    background-color: #F8F9FA;
    min-height: calc(100vh - 44px);
  }

  .sr-list-wrapper {
    padding: 1px 0;
  }

  .sr-item-card {
    background: #FFFFFF;
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.07);
    border-radius: 4px;
    margin: 20px 17px;
    position: relative;
    padding: 50px 20px 20px;
  }

  .sr-status-recommended {
    background: var(--wo-biz-theme-gradient-3);
    border-radius: 50px 0 0 50px;
    height: 22px;
    width: 58px;
    font-size: 13px;
    color: #FFFFFF;
    text-align: center;
    font-weight: 400;
    line-height: 22px;
    position: absolute;
    top: 17px;
    right: 0;
  }

  .sr-status-contacted {
    background: linear-gradient(45deg, #09acf1 0%, #4c73ff 100%);
    border-radius: 50px 0 0 50px;
    height: 22px;
    width: 58px;
    font-size: 13px;
    color: #FFFFFF;
    text-align: center;
    font-weight: 400;
    line-height: 22px;
    position: absolute;
    top: 17px;
    right: 0;
  }

  .sr-item-details {
    margin: 0;
  }

  .sr-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    dt {
      font-size: 14px;
      color: #4A5568;
      font-weight: 400;
      margin: 0;
    }

    dd {
      font-size: 14px;
      color: #4A5568;
      font-weight: 400;
      margin: 0;
      text-align: right;
      max-width: 60%;
      word-break: break-all;
    }
  }

  .sr-list-footer {
    display: flex;
    flex-direction: column;
    padding: 0 17px 20px;

    .sr-footer-title,
    .sr-footer-text,
    .sr-footer-contact {
      font-size: 14px;
      color: #718096;
      line-height: 1.5;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .sr-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 20px;
    min-height: 50vh;

    img {
      margin-bottom: 20px;
    }
  }

  .sr-empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .sr-empty-title {
      font-size: 16px;
      color: #718096;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .sr-empty-text,
    .sr-empty-contact {
      font-size: 14px;
      color: #718096;
      line-height: 1.5;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
