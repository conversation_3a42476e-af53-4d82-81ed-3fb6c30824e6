<!--
/**
 * 数字乡村首页组件
 *
 * 主要功能：
 * 1. 展示数字乡村电商平台的首页内容，包含轮播图、热门商品推荐和瀑布流商品列表
 * 2. 提供商品搜索、分类浏览、商品详情跳转等核心电商功能
 * 3. 集成用户登录状态管理，支持订单查看和用户相关操作
 * 4. 实现响应式设计和骨架屏加载效果，提升用户体验
 * 5. 支持供应商推荐功能，引导用户了解本地农产品销售信息
 * 6. 采用瀑布流布局展示商品，支持无限滚动加载
 *
 * 技术特点：
 * - 基于BaseHomeLayout布局组件，复用通用首页结构
 * - 使用Composition API和组合式函数管理状态和逻辑
 * - 集成骨架屏和过渡动画，优化加载体验
 * - 支持防抖处理，避免频繁操作
 * - 实现模块化数据管理和异步加载
 *
 * 使用场景：
 * - 数字乡村电商平台的主入口页面
 * - 农产品展示和销售的核心界面
 * - 用户浏览和购买商品的起始页面
 */
-->

<template>
  <!-- 数字乡村首页主容器 -->
  <!-- 使用BaseHomeLayout作为基础布局，配置显示模块和样式类名 -->
  <!-- 传入轮播图数据、网格菜单项、骨架屏状态等配置 -->
  <!-- 监听搜索、轮播图点击、网格项点击等用户交互事件 -->
  <BaseHomeLayout
    :show-modules="showModulesConfig"
    home-class="dv-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <!-- 额外内容插槽：热门商品推荐区域 -->
    <template #additional-content>
      <!-- 热门商品推荐区域容器 -->
      <!-- 根据shouldShowRecommend计算属性控制显示 -->
      <section
        v-if="shouldShowRecommend"
        class="dv-recommend-section"
      >
        <!-- 骨架屏与实际内容的过渡动画 -->
        <!-- 使用skeleton-fade过渡效果，提供平滑的加载体验 -->
        <Transition name="skeleton-fade" mode="out-in">
          <!-- 推荐区域骨架屏，在数据加载时显示 -->
          <RecommendSkeleton v-if="skeletonStates.recommend" key="recommend-skeleton" />
          <!-- 热门商品推荐视图，传入热门商品数据 -->
          <RecommendView
            v-else
            key="recommend-content"
            :hot-goods="hotGoods"
          />
        </Transition>
      </section>
    </template>

    <!-- 主要内容插槽：瀑布流商品列表和底部操作栏 -->
    <template #main-content>
      <!-- 主要内容区域容器 -->
      <main class="dv-main-content">
        <!-- 瀑布流商品展示区域 -->
        <!-- 传入商品列表、加载状态、完成状态等瀑布流相关数据 -->
        <!-- 监听商品点击、加载更多、渲染完成等事件 -->
        <WaterfallSection
          class="dv-waterfall"
          :waterfall-goods-list="waterfallGoodsList"
          :waterfall-loading="waterfallLoading"
          :waterfall-finished="waterfallFinished"
          :waterfall-button-can-show="waterfallButtonCanShow"
          :waterfall-render-complete="waterfallRenderComplete"
          :skeleton-states="waterfallSkeletonStates"
          @goods-click="handleGoodsClick"
          @load-more="handleWaterfallLoadMore"
          @after-render="handleWaterfallAfterRender"
        />

        <!-- 订单浮动按钮 -->
        <!-- 固定定位在页面右侧，点击跳转到订单列表 -->
        <aside class="dv-order-float">
          <img
            src="./assets/order.png"
            alt="购物订单"
            loading="lazy"
            @click="loadOrderList"
          >
        </aside>

        <!-- 底部操作栏占位符，避免内容被底部栏遮挡 -->
        <WoActionBarPlaceholder :height="85" />
        <!-- 底部操作栏：供应商推荐入口 -->
        <WoActionBar class="dv-action-bar">
          <!-- 操作栏内容容器 -->
          <div class="dv-action-content">
            <!-- 扶贫图标装饰 -->
            <div class="dv-action-icon" aria-hidden="true"></div>
            <!-- 文字信息区域 -->
            <div class="dv-action-text">
              <!-- 主标题：本地农产品销售攻略 -->
              <span class="dv-action-title">本地农产品销售攻略</span>
              <!-- 副标题：本地供应商推荐 -->
              <span class="dv-action-subtitle">本地供应商推荐</span>
            </div>
            <!-- 查看详情按钮，点击跳转到智农商城页面 -->
            <WoButton
              type="gradient"
              size="small"
              round
              @click="recommend"
            >
              查看详情
            </WoButton>
          </div>
        </WoActionBar>
      </main>
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import RecommendView from '@views/Home/components/RecommendView.vue'
import RecommendSkeleton from '@views/Home/components/Skeleton/RecommendSkeleton.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'
import { getHotGoods } from '@/api/interface/digitalVillage'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom } from 'commonkit'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { closeToast, showLoadingToast } from 'vant'
import { useUserStore } from '@/store/modules/user'

// ==================== 首页数据管理 ====================
// 使用首页数据组合式函数，获取轮播图、商品列表、骨架屏状态等数据和方法
const {
  headerBannerList,           // 头部轮播图列表数据
  gridMenuItems,              // 网格菜单项数据
  skeletonStates,             // 各模块骨架屏显示状态
  moduleDataReady,            // 各模块数据准备就绪状态
  waterfallGoodsList,         // 瀑布流商品列表数据
  waterfallLoading,           // 瀑布流加载状态
  waterfallFinished,          // 瀑布流加载完成状态
  waterfallButtonCanShow,     // 瀑布流加载按钮显示状态
  waterfallRenderComplete,    // 瀑布流渲染完成状态
  getHeaderBannerList,        // 获取头部轮播图数据的方法
  getWaterfallList,           // 获取瀑布流商品列表的方法
  resetWaterfallState,        // 重置瀑布流状态的方法
  getPartionListData,         // 获取分区列表数据的方法
  hideSkeletonInOrder         // 按顺序隐藏骨架屏的方法
} = useHomeData()

// ==================== 首页导航管理 ====================
// 使用首页导航组合式函数，获取各种点击事件处理方法
const {
  handleGoodsClick,           // 商品点击处理方法
  handleBannerClick,          // 轮播图点击处理方法
  handleGridItemClick,        // 网格项点击处理方法
  handleMoreClick,            // 更多按钮点击处理方法
  handleSearch,               // 搜索处理方法
} = useHomeNavigation()

// ==================== 路由和状态管理 ====================
// 获取路由实例，用于页面跳转
const router = useRouter()
// 获取用户状态管理实例，用于登录状态检查和用户操作
const userStore = useUserStore()

// ==================== 热门商品推荐相关状态 ====================
// 热门商品列表数据
const hotGoods = ref([])
// 商品分类列表数据
const typeList = ref([])
// 当前选中的商品池ID，用于筛选瀑布流商品
const goodsPoolIdSelected = ref('')

// ==================== 页面模块显示配置 ====================
// 计算属性：配置首页各模块的显示状态
// 数字乡村首页不显示搜索和网格菜单，只显示轮播图
const showModulesConfig = computed(() => ({
  search: false,              // 不显示搜索模块
  banner: true,               // 显示轮播图模块
  gridMenu: false             // 不显示网格菜单模块
}))

// 计算属性：判断是否应该显示推荐区域
// 当骨架屏显示或有热门商品数据时显示推荐区域
const shouldShowRecommend = computed(() =>
  skeletonStates.value.recommend || hotGoods.value.length > 0
)

// 计算属性：瀑布流骨架屏状态配置
// 从全局骨架屏状态中提取瀑布流相关状态
const waterfallSkeletonStates = computed(() => ({
  waterfall: skeletonStates.value.waterfall
}))

// 初始化推荐区域骨架屏状态为显示
skeletonStates.value = {
  ...skeletonStates.value,
  recommend: true
}

// 初始化推荐区域数据准备状态为未就绪
moduleDataReady.value = {
  ...moduleDataReady.value,
  recommend: false
}

// ==================== 用户订单相关操作 ====================
// 订单列表加载处理函数，使用防抖避免频繁点击
// 检查用户登录状态，已登录则跳转订单列表，未登录则触发登录
const loadOrderList = debounce(async () => {
  // 查询当前用户登录状态
  await userStore.queryLoginStatus()
  if (userStore.isLogin) {
    // 用户已登录，直接跳转到订单列表页面
    router.push({ path: '/user/order/list' })
  } else {
    // 用户未登录，触发登录流程，不重新加载页面
    await userStore.login({ reload: false })
  }
}, 300)

// ==================== 供应商推荐相关操作 ====================
// 供应商推荐页面跳转处理函数
// 点击底部操作栏的查看详情按钮时触发
const recommend = () => {
  // 跳转到智农商城页面
  router.push('/digitalVillage/znShop')
}

// ==================== 瀑布流相关操作 ====================
// 瀑布流渲染完成后的处理函数
// 设置瀑布流渲染完成状态为true
const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

// 瀑布流加载更多处理函数，使用防抖避免频繁触发
// 调用getWaterfallList方法加载更多商品数据
const handleWaterfallLoadMore = debounce(() => {
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}, 200)

// 切换商品池处理函数
// 更新选中的商品池ID，重置瀑布流状态，重新加载商品列表
const changeGoodsPool = (id, sortType = '') => {
  // 更新当前选中的商品池ID
  goodsPoolIdSelected.value = id
  // 重置瀑布流状态，清空现有数据
  resetWaterfallState()
  // 根据新的商品池ID获取商品列表
  getWaterfallList(id, sortType, false)
}

// ==================== 页面初始化相关操作 ====================
// 页面数据初始化函数
// 获取商品分区列表，设置默认选中的商品池
const initPage = async () => {
  try {
    // 获取分区列表数据，参数2表示获取特定类型的分区
    const partionList = await getPartionListData(2)
    typeList.value = partionList

    // 如果有分区数据，选择第一个分区作为默认商品池
    if (typeList.value.length > 0) {
      const firstRecommend = typeList.value[0]
      goodsPoolIdSelected.value = firstRecommend.id
      // 切换到第一个推荐分区的商品池
      changeGoodsPool(firstRecommend.id)
    }
  } catch (error) {
    console.error('初始化页面数据失败:', error)
  }
}

// 热门商品数据初始化函数
// 获取热门商品列表，处理加载状态和骨架屏显示
const initHotGoods = async () => {
  // 构建请求参数，包含页面标识、业务代码、渠道信息
  const params = {
    showPage: '1',                    // 页面标识
    bizCode: getBizCode('GOODS'),     // 商品业务代码
    channel: curChannelBiz.get()      // 当前渠道业务标识
  }

  // 非联通环境下显示加载提示
  if (!isUnicom) {
    showLoadingToast()
  }

  try {
    // 调用热门商品接口获取数据
    const [err, json] = await getHotGoods(params)

    if (err) {
      // 接口返回错误，记录错误信息并清空商品列表
      console.error('获取热门商品失败:', err.msg)
      hotGoods.value = []
    } else {
      // 接口调用成功，更新热门商品列表数据
      hotGoods.value = json || []
    }
  } catch (error) {
    // 接口调用异常，记录异常信息并清空商品列表
    console.error('获取热门商品异常:', error)
    hotGoods.value = []
  } finally {
    // 无论成功或失败，都要执行的清理操作
    if (!isUnicom) {
      // 非联通环境下关闭加载提示
      closeToast()
    }
    // 标记推荐模块数据已准备就绪
    moduleDataReady.value.recommend = true
    // 等待DOM更新完成
    await nextTick()
    // 按顺序隐藏骨架屏，提供平滑的加载体验
    await hideSkeletonInOrder(['banner', 'gridMenu', 'recommend'])
  }
}

// ==================== 生命周期钩子 ====================
// 组件挂载时的初始化操作
// 并行执行多个初始化任务，提高页面加载效率
onMounted(async () => {
  // 使用Promise.allSettled并行执行初始化任务
  // 即使某个任务失败，其他任务仍会继续执行
  await Promise.allSettled([
    getHeaderBannerList(),    // 获取头部轮播图数据
    initHotGoods(),           // 初始化热门商品数据
    initPage()                // 初始化页面基础数据
  ])
})
</script>


<style scoped lang="less">
.dv-home {
  min-height: 100vh;

  .dv-recommend-section {
    padding: 0 10px;
    position: relative;
    box-sizing: border-box;
  }

  .dv-main-content {
    position: relative;
  }

  .dv-waterfall {
    :deep(.home-waterfall-container) {
      padding: 0 10px;
    }
  }

  .dv-order-float {
    position: fixed;
    width: 100px;
    height: auto;
    right: 0;
    top: 150px;
    z-index: 999;

    img {
      width: 100%;
      height: auto;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .dv-action-bar {
    padding: 0;

    .dv-action-content {
      background: #ffffff;
      box-shadow: 0 -9px 32px 0 rgba(0, 0, 0, 0.07);
      min-height: 65px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
      padding: 0 12px;
    }

    .dv-action-icon {
      background-image: url("./assets/fupin.png");
      height: 54px;
      width: 54px;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
      flex-shrink: 0;
    }

    .dv-action-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      min-width: 0;
    }

    .dv-action-title {
      font-size: 16px;
      color: #00200a;
      font-weight: 500;
      line-height: 1.2;
    }

    .dv-action-subtitle {
      font-size: 12px;
      color: #828282;
      font-weight: 400;
      margin-top: 4px;
      line-height: 1.2;
    }

    :deep(.wo-button) {
      flex-shrink: 0;
    }
  }
}

// 骨架屏过渡动画
.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}

// 响应式优化
@media (max-width: 375px) {
  .dv-home {
    .dv-action-content {
      gap: 8px;
      padding: 0 8px;
    }

    .dv-action-icon {
      width: 48px;
      height: 48px;
    }

    .dv-action-title {
      font-size: 14px;
    }

    .dv-action-subtitle {
      font-size: 11px;
    }
  }
}
</style>
