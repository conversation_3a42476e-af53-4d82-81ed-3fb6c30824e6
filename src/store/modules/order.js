/**
 * @fileoverview 订单状态管理模块
 * @description 管理订单相关的状态，包括各种状态的订单数量统计和获取
 */

import { defineStore } from 'pinia'
import { getOrderCount } from '@/api/interface/order'
import { debounce } from 'lodash-es'

/**
 * 订单状态管理Store
 * @description 管理订单相关状态，包括未支付、待发货、待收货订单数量的统计和缓存
 * @function useOrderStore
 * @returns {Object} Pinia store实例
 * @example
 * import { useOrderStore } from '@/store/modules/order'
 *
 * const orderStore = useOrderStore()
 * await orderStore.fetchOrderCount() // 获取订单数量
 * console.log(orderStore.hasUnprocessedOrders) // 检查是否有未处理订单
 */
export const useOrderStore = defineStore('order', {
  /**
   * 订单状态数据
   * @returns {Object} 状态对象
   * @property {number} unpaidCount - 未支付订单数量
   * @property {number} processedCount - 待发货订单数量
   * @property {number} dispatchedCount - 待收货订单数量
   * @property {boolean} loading - 数据加载状态
   * @property {number} lastFetchTime - 上次获取数据的时间戳，用于缓存控制
   */
  state: () => ({
    unpaidCount: 0,      // 未支付订单数
    processedCount: 0,   // 待发货订单数
    dispatchedCount: 0,  // 待收货订单数
    loading: false,      // 加载状态
    lastFetchTime: 0     // 上次获取时间，用于缓存
  }),

  getters: {
    /**
     * 根据订单类型获取对应的订单数量
     * @param {Object} state - 当前状态
     * @returns {Function} 返回一个函数，接收订单类型参数
     * @param {string} type - 订单类型 ('0': 未支付, '3': 待发货, '5': 待收货)
     * @returns {number} 对应类型的订单数量
     * @example
     * const orderStore = useOrderStore()
     * const unpaidCount = orderStore.getCountByType('0') // 获取未支付订单数
     */
    getCountByType: (state) => (type) => {
      switch (type) {
        case '0': return state.unpaidCount
        case '3': return state.processedCount
        case '5': return state.dispatchedCount
        default: return 0
      }
    },

    /**
     * 检查是否有任何未处理的订单
     * @param {Object} state - 当前状态
     * @returns {boolean} 如果有未支付、待发货或待收货订单则返回true
     * @example
     * const orderStore = useOrderStore()
     * if (orderStore.hasUnprocessedOrders) {
     *   console.log('有未处理的订单')
     * }
     */
    hasUnprocessedOrders: (state) => {
      return state.unpaidCount > 0 || state.processedCount > 0 || state.dispatchedCount > 0
    }
  },

  actions: {
    /**
     * 设置订单数量
     * @description 批量更新各种状态的订单数量
     * @param {Object} counts - 订单数量对象
     * @param {number} [counts.unpayCount=0] - 未支付订单数量
     * @param {number} [counts.payedCount=0] - 待发货订单数量
     * @param {number} [counts.deliverCount=0] - 待收货订单数量
     * @returns {void}
     * @example
     * const orderStore = useOrderStore()
     * orderStore.setOrderCounts({ unpayCount: 5, payedCount: 3, deliverCount: 2 })
     */
    setOrderCounts({ unpayCount = 0, payedCount = 0, deliverCount = 0 }) {
      this.unpaidCount = unpayCount
      this.processedCount = payedCount
      this.dispatchedCount = deliverCount
    },

    /**
     * 防抖获取订单数量
     * @description 使用防抖机制获取订单数量，避免频繁请求
     * @function fetchOrderCountDebounced
     * @returns {Promise<void>}
     * @example
     * const orderStore = useOrderStore()
     * orderStore.fetchOrderCountDebounced() // 300ms内多次调用只执行最后一次
     */
    fetchOrderCountDebounced: debounce(async function() {
      await this.fetchOrderCount()
    }, 300),

    /**
     * 获取订单数量
     * @description 从服务器获取各种状态的订单数量，支持缓存机制
     * @param {boolean} [force=false] - 是否强制刷新，忽略缓存
     * @returns {Promise<void>}
     * @example
     * const orderStore = useOrderStore()
     * await orderStore.fetchOrderCount() // 使用缓存
     * await orderStore.fetchOrderCount(true) // 强制刷新
     */
    async fetchOrderCount(force = false) {
      // 缓存5分钟
      const now = Date.now()
      if (!force && this.lastFetchTime && (now - this.lastFetchTime) < 5 * 60 * 1000) {
        return
      }

      if (this.loading) return

      this.loading = true
      try {
        const [err, data] = await getOrderCount()
        if (!err && data) {
          this.setOrderCounts(data)
          this.lastFetchTime = now
        }
      } catch (error) {
        console.error('获取订单数量失败:', error)
      } finally {
        this.loading = false
      }
    },

    /**
     * 清除缓存
     * @description 清除数据缓存时间戳，下次获取数据时将重新请求服务器
     * @returns {void}
     * @example
     * const orderStore = useOrderStore()
     * orderStore.clearCache() // 清除缓存
     */
    clearCache() {
      this.lastFetchTime = 0
    },

    /**
     * 重置状态
     * @description 重置所有订单相关状态到初始值
     * @returns {void}
     * @example
     * const orderStore = useOrderStore()
     * orderStore.reset() // 重置所有状态
     */
    reset() {
      this.unpaidCount = 0
      this.processedCount = 0
      this.dispatchedCount = 0
      this.loading = false
      this.lastFetchTime = 0
    }
  }
})
