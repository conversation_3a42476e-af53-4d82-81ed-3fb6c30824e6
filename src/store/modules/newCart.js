/**
 * @fileoverview 购物车状态管理模块
 * @description 基于 Pinia 的购物车状态管理，提供完整的购物车操作功能
 * 
 * 主要功能：
 * - 购物车数据查询和管理
 * - 商品增删改查操作
 * - 商品选中状态管理
 * - 库存检查和验证
 * - 失效商品处理
 * - 批量操作支持
 * 
 * 数据结构：
 * - validList: 有效商品分组列表 [{ groupName, goodsList, selected }, ...]
 * - invalidList: 失效商品分组列表 [{ groupName, goodsList }, ...]
 * - cartLoadingStatus: 购物车加载状态
 * 
 * 使用示例：
 * ```javascript
 * import { useNewCartStore } from '@/store/modules/newCart'
 * 
 * const cartStore = useNewCartStore()
 * 
 * // 查询购物车
 * const err = await cartStore.query()
 * 
 * // 获取购物车数据
 * const validGoods = cartStore.validList        // 有效商品列表
 * const invalidGoods = cartStore.invalidList    // 失效商品列表
 * const totalCount = cartStore.countAll         // 所有商品数量
 * const selectedCount = cartStore.selectCountAll // 选中商品数量
 * const totalPrice = cartStore.selectTotalPrice  // 选中商品总价
 * 
 * // 添加商品到购物车
 * await cartStore.add({ goodsId: '123', skuId: '456', goodsNum: 2, addressInfo: {...} })
 * 
 * // 更新商品数量
 * await cartStore.updateGoodsNum({ goodsId: '123', skuId: '456', goodsNum: 3 })
 * 
 * // 批量更新商品选中状态
 * await cartStore.updateGoodsSelectMuti([{ goodsId: '123', skuId: '456', select: true }])
 * 
 * // 移除商品
 * await cartStore.removeMuti([{ goodsId: '123', skuId: '456' }])
 * 
 * // 全选/反选操作
 * await cartStore.checkedAllValid()
 * 
 * // 按分组全选/反选
 * await cartStore.checkedAllByGroupName('默认分组')
 * ```
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
import { defineStore } from 'pinia'
import { cart } from '@/api'
import { getBizCode } from '@/utils/curEnv'
import { useUserStore } from './user'
import { woReport } from 'commonkit'
import dayjs from 'dayjs'
import { queryZqInfo } from '@/utils/zqInfo'
import { cloneDeep } from 'lodash-es'

/**
 * 数据保护装饰器 - 通过深拷贝隔离原始数据
 * @description 防止外部直接修改状态数据，确保数据的不可变性
 * @param {any} data - 需要保护的数据
 * @returns {any} 深拷贝后的数据
 * @example
 * const protectedData = protectData(originalData)
 */
const protectData = (data) => {
  // 深拷贝确保数据隔离
  return cloneDeep(data)
}

/**
 * 数据完整性验证
 * @description 验证商品分组数据的完整性和格式正确性
 * @param {Object} group - 商品分组对象
 * @param {string} group.groupName - 分组名称
 * @param {Array} group.goodsList - 商品列表
 * @param {boolean} [group.selected] - 分组选中状态
 * @returns {boolean} 验证结果，true表示数据有效
 * @example
 * const isValid = validateGroupData({ groupName: '默认分组', goodsList: [] })
 */
const validateGroupData = (group) => {
  if (!group || typeof group !== 'object') {
    console.warn('无效的分组数据:', group)
    return false
  }
  if (!Array.isArray(group.goodsList)) {
    console.warn('分组商品列表格式错误:', group)
    return false
  }
  return true
}

/**
 * 创建商品索引Map，提高查找性能
 * @description 为购物车中的所有商品创建索引映射，支持快速查找
 * @param {Object} state - 购物车状态对象
 * @param {Array} state.validList - 有效商品分组列表
 * @param {Array} state.invalidList - 失效商品分组列表
 * @returns {Map<string, Object>} 商品索引Map，key为"goodsId_skuId"格式
 * @example
 * const goodsMap = createGoodsMap(state)
 * const goods = goodsMap.get('123_456')
 */
const createGoodsMap = (state) => {
  const goodsMap = new Map()

  // 索引有效商品
  state.validList.forEach(group => {
    group.goodsList?.forEach(goods => {
      const key = `${goods.cartGoodsId}_${goods.cartSkuId}`
      goodsMap.set(key, { goods, type: 'valid', group })
    })
  })

  // 索引无效商品
  state.invalidList.forEach(group => {
    group.goodsList?.forEach(goods => {
      const key = `${goods.cartGoodsId}_${goods.cartSkuId}`
      goodsMap.set(key, { goods, type: 'invalid', group })
    })
  })

  return goodsMap
}

/**
 * 通过 goodsId 和 skuId 获取商品信息 - 优化版本
 * @description 在购物车中查找指定的商品信息
 * @param {Object} state - 购物车状态对象
 * @param {string|number} goodsId - 商品ID
 * @param {string|number} skuId - SKU ID
 * @returns {Object|null} 商品信息对象，未找到时返回null
 * @example
 * const goods = getGoods(state, '123', '456')
 * if (goods) {
 *   console.log('商品名称:', goods.goodsName)
 * }
 */
const getGoods = (state, goodsId, skuId) => {
  const goodsMap = createGoodsMap(state)
  const key = `${goodsId}_${skuId}`
  const result = goodsMap.get(key)
  return result?.goods || null
}

/**
 * 获取已经选中（未选中）的商品列表 - 优化版本
 * @description 根据选中状态和分组名称筛选商品
 * @param {Object} state - 购物车状态对象
 * @param {boolean} select - 选中状态，true获取选中商品，false获取未选中商品
 * @param {string} [groupName=''] - 分组名称，为空时查询所有分组
 * @returns {Array<Object>} 符合条件的商品列表
 * @example
 * // 获取所有选中的商品
 * const selectedGoods = getSelectGoods(state, true)
 * 
 * // 获取指定分组中未选中的商品
 * const unselectedGoods = getSelectGoods(state, false, '默认分组')
 */
const getSelectGoods = (state, select, groupName = '') => {
  const selected = select ? 'true' : 'false'
  const result = []

  state.validList.forEach(group => {
    if (groupName && group.groupName !== groupName) return

    group.goodsList?.forEach(goods => {
      if (goods.selected === selected) {
        result.push(goods)
      }
    })
  })

  return result
}



/**
 * 购物车查询状态常量
 * @description 定义购物车数据查询的各种状态
 * @readonly
 * @enum {string}
 */
export const CART_QUERY_STATUS = {
  /** 查询成功 */
  SUCCESS: 'success',
  /** 查询失败 */
  FAILED: 'failed',
  /** 正在加载 */
  LOADING: 'loading'
}

/**
 * 购物车状态管理Store
 * @description 基于Pinia的购物车状态管理，提供完整的购物车操作功能
 * @returns {Object} Pinia Store实例
 */
export const useNewCartStore = defineStore('newCart', {
  /**
   * 购物车状态数据
   * @returns {Object} 状态对象
   * @property {Array<Object>} validList - 有效商品分组列表
   * @property {Array<Object>} invalidList - 失效商品分组列表  
   * @property {string} cartLoadingStatus - 购物车加载状态
   */
  state: () => ({
    /** 有效商品分组列表 */
    validList: [],
    /** 失效商品分组列表 */
    invalidList: [],
    /** 购物车加载状态 */
    cartLoadingStatus: CART_QUERY_STATUS.LOADING
  }),
  /**
   * 计算属性（Getters）
   * @description 提供购物车数据的计算属性和状态查询
   */
  getters: {
    /**
     * 获取所有有效商品的总数量
     * @description 计算购物车中所有有效商品的数量总和
     * @returns {number} 商品总数量
     */
    countAll() {
      return this.validList.reduce((count, group) => {
        return count + (group.goodsList?.reduce((sum, goods) => sum + (goods.skuNum || 0), 0) || 0)
      }, 0)
    },
    
    /**
     * 获取购物车加载状态
     * @description 返回当前购物车的加载状态
     * @returns {string} 加载状态值
     */
    getCartLoadingStatus() {
      return this.cartLoadingStatus
    },
    
    /**
     * 获取有效商品列表
     * @description 返回购物车中的有效商品分组列表
     * @returns {Array<Object>} 有效商品分组列表
     */
    getCartValidList() {
      return this.validList
    },
    
    /**
     * 获取失效商品列表
     * @description 返回购物车中的失效商品分组列表
     * @returns {Array<Object>} 失效商品分组列表
     */
    getCartInvalidList() {
      return this.invalidList
    },
    
    /**
     * 获取所有有效商品的种类数
     * @description 计算购物车中有效商品的种类总数，如果登录状态下数量为0会自动重新查询
     * @returns {number} 商品种类数
     */
    countByGoods() {
      const count = this.validList.reduce((count, group) => {
        return count + (group.goodsList?.length || 0)
      }, 0)

      // 如果登录态且数量为0，重新拉取购物车数据
      if (count === 0) {
        const userStore = useUserStore()
        if (userStore.isLogin === true) {
          this.query()
        }
      }

      return count
    },
    
    /**
     * 获取选中商品的总数量
     * @description 计算购物车中已选中商品的数量总和
     * @returns {number} 选中商品总数量
     */
    selectCountAll() {
      return this.validList.reduce((count, group) => {
        return count + (group.goodsList?.reduce((sum, goods) => {
          return sum + (goods.selected === 'true' ? (goods.skuNum || 0) : 0)
        }, 0) || 0)
      }, 0)
    },
    
    /**
     * 获取选中商品的种类数
     * @description 计算购物车中已选中商品的种类总数
     * @returns {number} 选中商品种类数
     */
    selectCountByGoods() {
      return this.validList.reduce((count, group) => {
        return count + (group.goodsList?.filter(goods => goods.selected === 'true').length || 0)
      }, 0)
    },
    
    /**
     * 获取选中商品的总金额
     * @description 计算购物车中已选中商品的总价格，包含数值安全检查
     * @returns {number} 选中商品总金额
     */
    selectTotalPrice() {
      return this.validList.reduce((total, group) => {
        return total + (group.goodsList?.reduce((sum, goods) => {
          if (goods.selected === 'true') {
            const price = Number(goods.nowPrice) || 0
            const num = Number(goods.skuNum) || 0
            return sum + (price * num)
          }
          return sum
        }, 0) || 0)
      }, 0)
    },
    
    /**
     * 判断是否所有有效商品都已选中
     * @description 检查购物车中的所有有效商品分组是否都处于选中状态
     * @returns {boolean} true表示全选，false表示未全选
     */
    isSelectAll() {
      if (this.validList.length === 0) return false
      return this.validList.every(group => group.selected === true)
    },
    
    /**
     * 判断是否有商品
     * @description 检查购物车中是否存在有效商品
     * @returns {boolean} true表示有商品，false表示无商品
     */
    hasGoods() {
      return this.countByGoods > 0
    },
    
    /**
     * 判断是否有选中的商品
     * @description 检查购物车中是否存在已选中的商品
     * @returns {boolean} true表示有选中商品，false表示无选中商品
     */
    hasSelectedGoods() {
      return this.selectCountByGoods > 0
    },
    
    /**
     * 判断是否有失效商品
     * @description 检查购物车中是否存在失效的商品
     * @returns {boolean} true表示有失效商品，false表示无失效商品
     */
    hasInvalidGoods() {
      return this.invalidList.some(group => group.goodsList?.length > 0)
    },
    
    /**
     * 判断是否有有效商品
     * @description 检查购物车中是否存在有效的商品
     * @returns {boolean} true表示有有效商品，false表示无有效商品
     */
    hasValidGoods() {
      return this.validList.some(group => group.goodsList?.length > 0)
    }
  },
  /**
   * 操作方法（Actions）
   * @description 提供购物车的各种操作方法
   */
  actions: {
    /**
     * 设置有效商品列表
     * @description 更新购物车中的有效商品列表数据
     * @param {Array<Object>} payload - 有效商品分组列表
     * @example
     * // 设置有效商品列表
     * const cartStore = useNewCartStore()
     * cartStore.setValidList(validGoodsList)
     */
    setValidList(payload) {
      // 数据验证 + 深拷贝保护
      const validatedPayload = payload.filter(validateGroupData)
      this.validList = protectData(validatedPayload)
      this.selectCheck()
    },
    
    /**
     * 设置失效商品列表
     * @description 更新购物车中的失效商品列表数据
     * @param {Array<Object>} payload - 失效商品分组列表
     * @example
     * // 设置失效商品列表
     * const cartStore = useNewCartStore()
     * cartStore.setInvalidList(invalidGoodsList)
     */
    setInvalidList(payload) {
      // 数据验证 + 深拷贝保护
      const validatedPayload = payload.filter(validateGroupData)
      this.invalidList = protectData(validatedPayload)
    },
    
    /**
     * 设置购物车加载状态
     * @description 更新购物车的加载状态
     * @param {string} payload - 加载状态值
     * @example
     * // 设置加载状态
     * const cartStore = useNewCartStore()
     * cartStore.setCartLoadingStatus(CART_QUERY_STATUS.LOADING)
     */
    setCartLoadingStatus(payload) {
      this.cartLoadingStatus = payload
    },
    /**
     * 批量更新商品详细信息
     * @description 批量更新购物车中商品的数量和选中状态，支持同时更新多个商品
     * @param {Array<Object>} payload - 要更新的商品信息列表
     * @param {string|number} payload[].goodsId - 商品ID
     * @param {string|number} payload[].skuId - SKU ID
     * @param {number} [payload[].goodsNum] - 商品数量（可选）
     * @param {boolean} [payload[].select] - 选中状态（可选）
     * @example
     * // 批量更新商品信息
     * const cartStore = useNewCartStore()
     * cartStore.updateGoodsMuti([
     *   { goodsId: '123', skuId: '456', goodsNum: 3, select: true },
     *   { goodsId: '789', skuId: '012', goodsNum: 1, select: false }
     * ])
     */
    updateGoodsMuti(payload) {
      const newState = cloneDeep(this.$state)
      payload.forEach(load => {
        const { goodsId, skuId, goodsNum, select } = load
        const goods = getGoods(newState, goodsId, skuId)
        if (!goods) return
        if (typeof goodsNum === 'number') goods.skuNum = goodsNum
        if (typeof select === 'boolean') goods.selected = select ? 'true' : 'false'
      })
      this.setValidList(newState.validList)
      this.selectCheck()
    },
    /**
     * 批量移除商品数据
     * @description 从购物车中批量移除指定的商品，支持同时移除多个商品
     * @param {Array<Object>} payload - 要移除的商品信息列表
     * @param {string|number} payload[].goodsId - 商品ID
     * @param {string|number} payload[].skuId - SKU ID
     * @example
     * // 批量移除商品
     * const cartStore = useNewCartStore()
     * cartStore.removeGoodsMuti([
     *   { goodsId: '123', skuId: '456' },
     *   { goodsId: '789', skuId: '012' }
     * ])
     */
    removeGoodsMuti(payload) {
      const newState = cloneDeep(this.$state)
      payload.forEach(load => {
        const { goodsId, skuId } = load
        newState.validList.forEach(group => {
          group.goodsList = group.goodsList.filter(goods => {
            return !(goods.cartGoodsId === goodsId && goods.cartSkuId === skuId)
          })
        })
        newState.invalidList.forEach(group => {
          group.goodsList = group.goodsList.filter(goods => {
            return !(goods.cartGoodsId === goodsId && goods.cartSkuId === skuId)
          })
        })
      })
      this.setValidList(newState.validList)
      this.setInvalidList(newState.invalidList)
      this.hasGoodsCheck()
      this.selectCheck()
    },
    /**
     * 处理所有分组的选中状态展示
     * @description 检查并更新所有商品分组的选中状态，根据分组内商品的选中情况自动设置分组的选中状态
     * @returns {void}
     * @example
     * // 更新分组选中状态
     * const cartStore = useNewCartStore()
     * cartStore.selectCheck()
     * 
     * @note 当分组内所有商品都被选中时，分组状态为选中；否则为未选中
     */
    selectCheck() {
      const newState = cloneDeep(this.$state)
      newState.validList.forEach(group => {
        if (group.goodsList && group.goodsList.length > 0) {
          const selectedList = group.goodsList.filter(goods => goods.selected === 'true')
          group.selected = selectedList.length === group.goodsList.length
        } else {
          group.selected = false
        }
      })

      newState.validList.forEach(group => {
        protectData(group)
      })

      this.validList = newState.validList
    },
    /**
     * 商品分组是否还有商品检查
     * @description 检查并过滤掉没有商品的空分组，保持购物车数据的整洁性
     * @returns {void}
     * @example
     * // 清理空分组
     * const cartStore = useNewCartStore()
     * cartStore.hasGoodsCheck()
     * 
     * @note 本方法只是过滤空 group 数据，不改变存在数据的 group
     */
    hasGoodsCheck() {
      const newValidList = this.validList.filter(group => {
        return group.goodsList && group.goodsList.length > 0
      })
      this.validList = newValidList
    },
    /**
     * 处理用户登录
     * @description 调用用户store的登录方法，确保用户处于登录状态
     * @async
     * @returns {Promise<void>}
     * @example
     * // 确保用户登录
     * const cartStore = useNewCartStore()
     * await cartStore.login()
     * 
     * @note 登录时不会重新加载页面（reload: false）
     */
    async login() {
      const userStore = useUserStore()
      await userStore.login({ reload: false })
    },
    /**
     * 读取购物车内容
     * @description 查询并加载购物车数据，包括有效商品和失效商品列表
     * @async
     * @returns {Promise<null|Object>} 成功时返回null，失败时返回错误对象
     * @example
     * // 查询购物车数据
     * const cartStore = useNewCartStore()
     * const error = await cartStore.query()
     * if (!error) {
     *   console.log('购物车数据加载成功')
     * } else {
     *   console.error('购物车数据加载失败:', error)
     * }
     * 
     * @note 查询前会先获取用户默认地址信息，支持智企业务代码
     */
    async query() {
      // 需要先读取地址，再查询购物车
      const userStore = useUserStore()
      await userStore.queryDefaultAddr()
      const addr = userStore.curAddressInfo
      const addressInfo = JSON.stringify({
        provinceId: addr.provinceId,
        provinceName: addr.provinceName,
        cityId: addr.cityId,
        cityName: addr.cityName,
        countyId: addr.countyId,
        countyName: addr.countyName,
        townId: addr.townId,
        townName: addr.townName
      })
      this.setCartLoadingStatus(CART_QUERY_STATUS.LOADING)

      const bizCode = getBizCode('ORDER')
      const zqInfo = queryZqInfo()
      let params = {
        bizCode,
        addressInfo
      }

      if (bizCode === 'zq') {
        params = {
          bizCode,
          addressInfo,
          enterpriseCode: zqInfo.ciCode
        }
      }

      const [err, json] = await cart.query(params)
      if (!err) {
        this.setCartLoadingStatus(CART_QUERY_STATUS.SUCCESS)
        if (json && !json.goodsGroupList) {
          const timestamp = dayjs().format('YYYY-MM-DD hh:mm:ss')
          const params = {
            cartId: json.cartId,
            distriBizCode: json.distriBizCode
          }
          woReport(`购物车监控，分组数据出现丢失。 出现问题时间：${timestamp}`, params)
        }

        // 现要求所有分组合并成一组，特殊处理
        const list1 = json.goodsGroupList.map(group => {
          group.selected = false
          return group
        })

        const list2 = json.invalidGoodsList.filter(goods => {
          return goods.goods
        })
        const validList = list1.length > 0 ? list1 : []
        const invalidList = list2.length > 0 ? [{ groupName: '失效商品', goodsList: list2 }] : []

        this.setValidList(validList)
        this.setInvalidList(invalidList)
        return null
      } else {
        this.setCartLoadingStatus(CART_QUERY_STATUS.FAILED)
        return err
      }
    },
    /**
     * 快速读取购物车内容
     * @description 快速查询购物车数据，保持原有选中状态，过滤失效商品
     * @async
     * @returns {Promise<null|Object>} 成功时返回null，失败时返回错误对象
     * @example
     * // 快速查询购物车数据
     * const cartStore = useNewCartStore()
     * const error = await cartStore.queryQuick()
     * if (!error) {
     *   console.log('购物车数据快速加载成功')
     * }
     * 
     * @note 相比query方法，此方法会保持原有的分组选中状态，并自动过滤失效商品
     */
    async queryQuick() {
      // 需要先读取地址，再查询购物车
      const userStore = useUserStore()
      await userStore.queryDefaultAddr()
      const addr = userStore.curAddressInfo
      const addressInfo = JSON.stringify({
        provinceId: addr.provinceId,
        provinceName: addr.provinceName,
        cityId: addr.cityId,
        cityName: addr.cityName,
        countyId: addr.countyId,
        countyName: addr.countyName,
        townId: addr.townId,
        townName: addr.townName
      })
      this.setCartLoadingStatus(CART_QUERY_STATUS.LOADING)
      const [err, json] = await cart.baseView({ bizCode: getBizCode('ORDER'), addressInfo })
      if (!err) {
        this.setCartLoadingStatus(CART_QUERY_STATUS.SUCCESS)
        if (json && !json.goodsGroupList) {
          const timestamp = dayjs().format('YYYY-MM-DD hh:mm:ss')
          const params = {
            cartId: json.cartId,
            distriBizCode: json.distriBizCode
          }
          woReport(`购物车监控，分组数据出现丢失。 出现问题时间：${timestamp}`, params)
        }

        const oldCartValidList = this.validList
        let newCartValidList = json.goodsGroupList
        const oldCartInvalidList = this.invalidList
        // 1. 先看看分组是否勾选选了
        const oldItemsMap = new Map(oldCartValidList.map(item => [item.groupName, item.selected]))
        newCartValidList.forEach(newItem => {
          const selected = oldItemsMap.get(newItem.groupName)
          if (selected !== undefined) {
            newItem.selected = selected
          }
        })

        console.warn('oldCartInvalidList', oldCartInvalidList)

        // 创建一个包含所有无效商品ID的集合
        const invalidIds = new Set()
        oldCartInvalidList.forEach(oldItem => {
          oldItem.goodsList.forEach(oldCartInvalidItem => {
            invalidIds.add(oldCartInvalidItem.cartSkuId)
          })
        })

        // 使用这个集合来过滤newCartValidList中的有效商品
        newCartValidList = newCartValidList.map(newItem => {
          return {
            ...newItem,
            goodsList: newItem.goodsList.filter(item => !invalidIds.has(item.cartSkuId))
          }
        })

        console.warn('newCartValidList', newCartValidList)

        const validList = newCartValidList.length > 0 ? newCartValidList : []
        this.setValidList(validList)
        return null
      } else {
        this.setCartLoadingStatus(CART_QUERY_STATUS.FAILED)
        return err
      }
    },
    /**
     * 添加商品到购物车
     * @description 将指定商品添加到购物车，添加成功后自动刷新购物车数据
     * @async
     * @param {Object} payload - 商品信息
     * @param {string|number} payload.goodsId - 商品ID
     * @param {string|number} payload.skuId - SKU ID
     * @param {number} payload.goodsNum - 商品数量
     * @param {string} payload.addressInfo - 地址信息（JSON字符串）
     * @returns {Promise<null|Object>} 成功时返回null，失败时返回错误对象
     * @example
     * // 添加商品到购物车
     * const cartStore = useNewCartStore()
     * const error = await cartStore.add({
     *   goodsId: '123',
     *   skuId: '456',
     *   goodsNum: 2,
     *   addressInfo: JSON.stringify({...})
     * })
     * if (!error) {
     *   console.log('商品添加成功')
     * }
     */
    async add(payload) {
      await this.login()

      const { goodsId, skuId, goodsNum, addressInfo } = payload
      const bizCode = getBizCode('ORDER')
      const [err] = await cart.add({ goodsId, skuId, goodsNum, bizCode, addressInfo })
      if (!err) {
        this.query()
        return null
      } else {
        return err
      }
    },
    /**
     * 更新购物车商品数量
     * @description 更新指定商品的数量，包含库存检查和数量验证
     * @async
     * @param {Object} payload - 更新信息
     * @param {string|number} payload.goodsId - 商品ID
     * @param {string|number} payload.skuId - SKU ID
     * @param {number} payload.goodsNum - 新的商品数量（必须为Number类型）
     * @returns {Promise<null|Object>} 成功时返回null，失败时返回错误对象
     * @example
     * // 更新商品数量
     * const cartStore = useNewCartStore()
     * const error = await cartStore.updateGoodsNum({
     *   goodsId: '123',
     *   skuId: '456',
     *   goodsNum: 3
     * })
     * if (error) {
     *   console.error('更新失败:', error.msg)
     * }
     * 
     * @note 会自动检查库存限制和最小购买数量（至少1件）
     */
    async updateGoodsNum(payload) {
      await this.login()

      const { goodsId, skuId, goodsNum } = payload
      const goods = getGoods(this.$state, goodsId, skuId)
      if (!goods) return { code: 'FE2002', msg: '当前商品不存在', goodsId, skuId }
      const stock = Number(goods.goods.skuList[0].stock)
      if (goodsNum > stock) {
        return { code: 'FE2001', msg: '最多可购买' + stock + '件', stock }
      }
      if (goodsNum < 1) {
        return { code: 'FE2002', msg: '最少购买 1 件商品', stock }
      }
      const bizCode = getBizCode('ORDER')
      const [err] = await cart.update({ goodsId, skuId, goodsNum, bizCode })
      if (!err) {
        this.updateGoodsMuti([payload])
        return null
      } else {
        return err
      }
    },
    /**
     * 批量更新商品选中状态
     * @description 批量更新指定商品的选中状态，支持多个商品同时操作
     * @async
     * @param {Array<Object>} payload - 更新信息列表
     * @param {string|number} payload[].goodsId - 商品ID
     * @param {string|number} payload[].skuId - SKU ID
     * @param {boolean} payload[].select - 选中状态（true为选中，false为取消选中）
     * @returns {Promise<null|Object>} 成功时返回null，失败时返回错误对象
     * @example
     * // 批量更新商品选中状态
     * const cartStore = useNewCartStore()
     * const error = await cartStore.updateGoodsSelectMuti([
     *   { goodsId: '123', skuId: '456', select: true },
     *   { goodsId: '789', skuId: '012', select: false }
     * ])
     * if (!error) {
     *   console.log('选中状态更新成功')
     * }
     */
    async updateGoodsSelectMuti(payload) {
      await this.login()

      const bizCode = getBizCode('ORDER')
      const [err] = await cart.select({ cartGoodsList: JSON.stringify(payload), bizCode })
      if (!err) {
        this.updateGoodsMuti(payload)
        return null
      } else {
        return err
      }
    },
    /**
     * 批量移除购物车商品
     * @description 批量移除指定的购物车商品，支持多个商品同时删除
     * @async
     * @param {Array<Object>} payload - 要移除的商品列表
     * @param {string|number} payload[].goodsId - 商品ID
     * @param {string|number} payload[].skuId - SKU ID
     * @returns {Promise<null|Object>} 成功时返回null，失败时返回错误对象
     * @example
     * // 批量移除商品
     * const cartStore = useNewCartStore()
     * const error = await cartStore.removeMuti([
     *   { goodsId: '123', skuId: '456' },
     *   { goodsId: '789', skuId: '012' }
     * ])
     * if (!error) {
     *   console.log('商品移除成功')
     * }
     */
    async removeMuti(payload) {
      await this.login()

      const bizCode = getBizCode('ORDER')
      const [err] = await cart.remove({ goodsSkuList: JSON.stringify(payload), bizCode })
      if (!err) {
        this.removeGoodsMuti(payload)
        return null
      } else {
        return err
      }
    },
    /**
     * 移除失效商品
     * @description 移除购物车中所有失效的商品
     * @async
     * @param {Object} payload - 删除参数
     * @param {Array} payload.deleteGoodsList - 要删除的失效商品列表
     * @returns {Promise<null|Object>} 成功时返回null，失败时返回错误对象
     * @example
     * // 移除所有失效商品
     * const cartStore = useNewCartStore()
     * const error = await cartStore.removeInvalidGoods({
     *   deleteGoodsList: [...]
     * })
     * if (!error) {
     *   console.log('失效商品移除成功')
     * }
     * 
     * @note 只会移除失效商品列表中的商品，不影响有效商品
     */
    async removeInvalidGoods(payload) {
      await this.login()
      const { deleteGoodsList } = payload
      const bizCode = getBizCode('ORDER')
      const [err] = await cart.removeInvalidGoods({ bizCode, deleteGoodsList })
      if (!err) {
        this.setInvalidList([])
        return null
      } else {
        return err
      }
    },
    /**
     * 删除有效区选中的商品
     * @description 删除购物车有效商品区域中所有被选中的商品
     * @async
     * @returns {Promise<null|Object>} 成功时返回null，失败时返回错误对象
     * @example
     * // 删除有效区选中的商品
     * const cartStore = useNewCartStore()
     * const error = await cartStore.removeSelectValidGoods()
     * if (!error) {
     *   console.log('选中商品删除成功')
     * }
     * 
     * @note 只删除有效商品列表中被选中的商品，不影响未选中的商品
     */
    async removeSelectValidGoods() {
      await this.login()
      const removedList = getSelectGoods(this.$state, true).map(goods => {
        return { goodsId: goods.cartGoodsId, skuId: goods.cartSkuId }
      })
      if (removedList.length > 0) {
        return this.removeMuti(removedList)
      }
    },
    /**
     * 购物车有效商品全选反选
     * @description 根据当前选中状态自动执行全选或反选操作，如果存在未选中商品则全选，否则反选
     * @async
     * @returns {Promise<null|Object>} 成功时返回null，失败时返回错误对象
     * @example
     * // 智能全选/反选
     * const cartStore = useNewCartStore()
     * const error = await cartStore.checkedAllValid()
     * if (!error) {
     *   console.log('操作成功')
     * }
     * 
     * @note 会自动判断当前状态：如果有未选中商品则执行全选，如果全部选中则执行反选
     */
    async checkedAllValid() {
      await this.login()

      // 查询未选中商品个数
      const unselectCount = getSelectGoods(this.$state, false).length
      // 如果商品存在未选中情况，则执行全选，否则执行反选
      const selectType = unselectCount > 0
      // 需要改变状态的商品列表
      const newPayload = getSelectGoods(this.$state, !selectType).map(goods => {
        return { goodsId: goods.cartGoodsId, skuId: goods.cartSkuId, select: selectType }
      })

      if (newPayload.length === 0) return null
      return this.updateGoodsSelectMuti(newPayload)
    },
    /**
     * 按分组全选反选
     * @description 根据分组内商品的当前选中状态自动执行全选或反选操作
     * @async
     * @param {string} payload - 分组名称
     * @returns {Promise<null|Object>} 成功时返回null，失败时返回错误对象
     * @example
     * // 对指定分组执行智能全选/反选
     * const cartStore = useNewCartStore()
     * const error = await cartStore.checkedAllByGroupName('默认分组')
     * if (!error) {
     *   console.log('分组操作成功')
     * }
     * 
     * @note 会自动判断分组内商品状态：如果有未选中商品则全选，如果全部选中则反选
     */
    async checkedAllByGroupName(payload) {
      await this.login()

      // 查询未选中商品个数
      const unselectCount = getSelectGoods(this.$state, false, payload).length
      // 如果商品存在未选中情况，则执行全选，否则执行反选
      const selectType = unselectCount > 0
      // 需要改变状态的商品列表
      const newPayload = getSelectGoods(this.$state, !selectType, payload).map(goods => {
        return { goodsId: goods.cartGoodsId, skuId: goods.cartSkuId, select: selectType }
      })

      if (newPayload.length === 0) return null
      return this.updateGoodsSelectMuti(newPayload)
    },
    /**
     * 检查商品库存状态
     * @description 根据商品SKU信息和购买数量检查库存状态，支持不同供货商的库存检查逻辑
     * @async
     * @param {Object} payload - 库存检查参数
     * @param {Object} payload.sku - 商品SKU信息
     * @param {string|number} payload.sku.stock - 库存数量
     * @param {string} payload.sku.supplierCode - 供货商代码（'jd'表示京东）
     * @param {string} [payload.sku.supplierSkuId] - 供货商SKU ID
     * @param {number} payload.skuNum - 购买数量
     * @returns {Promise<Object>} 库存检查结果
     * @returns {boolean} returns.flag - 库存是否充足（true充足，false不足，null未知）
     * @returns {number|null} returns.num - 具体库存数量（null表示数量未知但充足）
     * @example
     * // 检查商品库存
     * const cartStore = useNewCartStore()
     * const result = await cartStore.checkStock({
     *   sku: {
     *     stock: '10',
     *     supplierCode: 'normal'
     *   },
     *   skuNum: 2
     * })
     * if (result.flag) {
     *   console.log('库存充足，可购买数量:', result.num)
     * } else {
     *   console.log('库存不足')
     * }
     * 
     * @note 京东商品目前默认无库存，非京东商品根据业管配置的库存数量判断
     */
    async checkStock(payload) {
      const { sku, skuNum } = payload
      const result = { flag: true, num: null }

      // sku.stock=0 表示无货（业管配置）
      if (String(sku.stock) === '0') {
        result.flag = false
        result.num = 0
        return result
      }
      // 无供货商skuId则认为无货（20220523，后端郝孝虎说这个字段不再使用了，by LIJINGCHEN）
      // if (!sku.supplierSkuId) {
      //   result.flag = false
      //   result.num = 0
      //   return result
      // }
      // 非京东供货商返回业管配置的商品库存
      if (sku.supplierCode !== 'jd') {
        if (!sku.stock) {
          // 库存未知（可能是存量数据），认为库存充足
          result.flag = true
          result.num = null
        } else {
          // 有具体库存数量
          result.flag = Number(sku.stock) >= skuNum
          result.num = Number(sku.stock)
        }
        return result
      }

      // 走到此处，是京东逻辑，由于目前没有京东商品了，故直接算作库存为0
      result.flag = false
      result.num = 0
      return result
    },
    
    /**
     * 清空购物车
     * @description 清空购物车的所有数据，包括有效商品列表和失效商品列表
     * @example
     * // 清空购物车
     * const cartStore = useNewCartStore()
     * cartStore.clear()
     * console.log('购物车已清空')
     * 
     * @note 此操作会立即清空本地状态，不会调用后端API
     */
    clear() {
      this.validList = []
      this.invalidList = []
      this.cartLoadingStatus = CART_QUERY_STATUS.LOADING
    },
    
    /**
     * 添加商品到购物车
     * @description 将商品添加到购物车，添加成功后自动刷新购物车数据
     * @async
     * @param {Object} goods - 要添加的商品信息
     * @param {string} goods.skuId - 商品SKU ID
     * @param {number} goods.skuNum - 商品数量
     * @param {string} [goods.bizCode] - 业务代码
     * @returns {Promise<Object>} 操作结果
     * @returns {boolean} returns.success - 是否成功
     * @returns {string} returns.message - 操作消息
     * @example
     * // 添加商品到购物车
     * const cartStore = useNewCartStore()
     * const result = await cartStore.addGoods({
     *   skuId: '12345',
     *   skuNum: 2,
     *   bizCode: 'normal'
     * })
     * if (result.success) {
     *   console.log('添加成功')
     * }
     */
    async addGoods(goods) {
      try {
        const res = await cartAdd(goods)
        if (res.code === 200) {
          // 添加成功后重新查询购物车
          await this.query()
          return { success: true, message: '添加成功' }
        } else {
          return { success: false, message: res.message || '添加失败' }
        }
      } catch (error) {
        console.error('添加商品失败:', error)
        return { success: false, message: '网络错误，请重试' }
      }
    }
  }
})
