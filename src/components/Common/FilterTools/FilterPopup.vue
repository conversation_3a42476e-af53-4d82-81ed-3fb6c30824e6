<!--
功能描述：商品筛选弹窗组件
主要功能：
- 提供商品筛选功能，包括服务筛选（仅看有货）、价格区间筛选、品牌筛选
- 支持地址切换功能，显示当前配送地址并提供切换入口
- 根据搜索关键词或分类ID动态获取品牌列表
- 提供筛选条件的重置和确认功能，支持取消时恢复原始状态
- 自动处理价格区间的合理性校验（最低价不能大于最高价）
-->
<template>
  <!-- 右侧滑出的筛选弹窗：占屏幕宽度80%，从右侧滑入 -->
  <van-popup v-model:show="isVisible" closeable position="right" :close-on-click-overlay="true"
    :style="{ width: '80%', height: '100vh', overflow: 'hidden' }" @open="popupOpen" @close="popupClose">
    <!-- 筛选内容区域：包含所有筛选选项 -->
    <div class="filter-area">
      <div class="filter-area-content">
        <!-- 地址信息区域：显示当前配送地址和切换入口 -->
        <div class="filter-area-base">
          <div class="filter-area-address">
            <!-- 地址显示区域：图标+地址文本 -->
            <div class="filter-area-address-left">
              <img class="filter-area-address-icon" src="../../../static/images/address-icon.png" alt="">
              <span class="filter-area-address-title">{{ locationText }}</span>
            </div>
            <!-- 地址切换按钮：点击触发地址切换弹窗 -->
            <div class="filter-area-address-right" @click="setSwitchAddressPopupShow">
              <span class="filter-area-address-op">切换地址</span>
            </div>
          </div>
        </div>
        <!-- 服务筛选区域：提供"仅看有货"选项 -->
        <div class="filter-area-base">
          <h3 class="title">服务</h3>
          <ul class="filter-criteria-list">
            <!-- 仅看有货选项：点击切换选中状态 -->
            <li class="condition" :class="{ 'active': filterCriteria.isStock }" @click="onIsStock">仅看有货</li>
          </ul>
        </div>
        <!-- 价格区间筛选区域：最低价格和最高价格输入 -->
        <div class="filter-area-base">
          <h3 class="title">价格区间</h3>
          <div class="price-range">
            <!-- 最低价格输入框：限制7位数字，失焦时校验价格合理性 -->
            <van-field v-model="filterCriteria.minPrice" maxlength="7" type="digit" label="" placeholder="最低价格"
              class="input-number" @blur="onFilterCriteriaPriceBlur" />
            <!-- 价格区间分隔线 -->
            <div class="input-slicing"></div>
            <!-- 最高价格输入框：限制7位数字，失焦时校验价格合理性 -->
            <van-field v-model="filterCriteria.maxPrice" maxlength="7" type="digit" label="" placeholder="最高价格"
              class="input-number" @blur="onFilterCriteriaPriceBlur" />
          </div>
        </div>
        <!-- 品牌筛选区域：仅在有品牌数据时显示 -->
        <div class="filter-area-base" v-if="filterCriteria.brandsList.length > 0">
          <h3 class="title">品牌</h3>
          <ul class="filter-criteria">
            <!-- 品牌选项列表：网格布局显示所有品牌，支持多选 -->
            <li class="condition" :class="{ 'active': item.isSelected }"
              v-for="(item, index) in filterCriteria.brandsList" :key="index" @click="onBrandSelect(item)">{{ item.value
              }}</li>
          </ul>
        </div>
      </div>
    </div>
    <!-- 底部操作按钮区域：重置和确定按钮 -->
    <div class="filter-area-operator">
      <!-- 重置按钮：清空所有筛选条件 -->
      <WoButton type="secondary" size="medium" @click="filterResetBtnClick">
        重置
      </WoButton>
      <!-- 确定按钮：应用筛选条件并关闭弹窗 -->
      <WoButton type="primary" size="medium" @click="filterConfirmBtnClick">
        确定
      </WoButton>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed, watch, toRefs } from 'vue'
import { useRoute } from 'vue-router'
import { get } from 'lodash-es'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { skuPageListBrandList } from '@api/index.js'
import { getSearchBrandNames } from '@api/interface/search.js'
import { getBizCode } from '@utils/curEnv.js'

// ===================== 外部依赖和工具初始化 =======================
// 路由实例：用于获取查询参数
const route = useRoute()

// ===================== 组件属性定义和响应式处理 =======================
// 定义组件接收的属性配置
const props = defineProps({
  // 弹窗显示状态：控制筛选弹窗的显示和隐藏
  show: {
    type: Boolean,
    default: false
  },
  // 位置文本：显示当前配送地址信息
  locationText: {
    type: String,
    default: ''
  },
  // 分类ID：用于获取分类相关的品牌列表
  categoryId: {
    type: String,
    default: ''
  },
  // 搜索关键词：用于获取搜索相关的品牌列表
  keyword: {
    type: String,
    default: ''
  },
  // 筛选条件数据：包含所有筛选状态的对象
  modelValue: {
    type: Object,
    default: () => ({
      isStock: false,
      minPrice: '',
      maxPrice: '',
      brandsList: []
    })
  }
})

// 使用 toRefs 解构 props 以保持响应性
const { show, locationText, categoryId, keyword, modelValue } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['update:show', 'update:modelValue', 'switch-address', 'confirm', 'reset'])

// ===================== 弹窗显示状态管理 =======================
// 内部弹窗显示状态：双向绑定弹窗显示状态
const isVisible = computed({
  get: () => show.value,
  set: (value) => emit('update:show', value)
})

// 筛选条件响应式状态：双向绑定筛选条件数据
const filterCriteria = computed({
  get: () => modelValue.value,
  set: (value) => emit('update:modelValue', value)
})

// ===================== 品牌数据加载状态管理 =======================
// 品牌列表加载状态：控制品牌数据获取时的loading状态
const brandsLoading = ref(false)
// 品牌数据缓存标识：避免重复加载相同的品牌数据
const brandsDataLoaded = ref(false)

// 备份筛选条件：用于取消操作时恢复原始状态
const backupFilterCriteria = ref({
  isStock: false,
  minPrice: '',
  maxPrice: '',
  brandsList: []
})

// 监听搜索关键词变化：当关键词改变时重置品牌数据
watch(keyword, () => {
  // 重置品牌数据加载状态
  brandsDataLoaded.value = false
  // 清空当前品牌列表
  const newFilterCriteria = { ...filterCriteria.value }
  newFilterCriteria.brandsList = []
  emit('update:modelValue', newFilterCriteria)
})

// ===================== 品牌数据获取和管理 =======================
// 获取品牌列表数据：根据搜索关键词或分类ID获取对应的品牌列表
const fetchBrandsList = async () => {
  // 如果品牌数据已加载，避免重复请求
  if (brandsDataLoaded.value) {
    return
  }
  // 设置加载状态
  brandsLoading.value = true
  try {
    let err, data

    // 根据是否有搜索关键词判断使用哪个API
    if (keyword.value) {
      // 搜索场景：使用搜索品牌名称API
      const testDMX = get(route.query, 'testDMX', 'false')
        ;[err, data] = await getSearchBrandNames({
          bizCode: getBizCode('QUERY'),
          keyword: keyword.value,
          testDMX
        })
    } else {
      // 分类场景：使用分类品牌列表API
      ;[err, data] = await skuPageListBrandList({
        bizCode: getBizCode(),
        categoryId: categoryId.value
      })
    }

    // 处理API返回的品牌数据
    if (!err && data) {
      // 将品牌数据转换为统一格式，添加选中状态
      const brandsList = data.map(brand => ({
        value: brand.name || brand.value || brand,
        isSelected: false
      }))

      // 更新筛选条件中的品牌列表
      const newFilterCriteria = { ...filterCriteria.value }
      newFilterCriteria.brandsList = brandsList
      emit('update:modelValue', newFilterCriteria)

      // 备份品牌列表数据用于恢复操作
      backupFilterCriteria.value.brandsList = JSON.parse(JSON.stringify(brandsList))
      // 标记品牌数据已加载
      brandsDataLoaded.value = true
    }
  } catch (error) {
    console.error('获取品牌列表失败:', error)
  } finally {
    // 清除加载状态
    brandsLoading.value = false
  }
}

// ===================== 弹窗生命周期事件处理 =======================
// 弹窗打开事件处理：检查并加载品牌数据
const popupOpen = () => {
  // 如果品牌数据未加载或品牌列表为空，则获取品牌数据
  if (!brandsDataLoaded.value || filterCriteria.value.brandsList.length === 0) {
    fetchBrandsList()
  }
}

// 弹窗关闭事件处理：恢复筛选条件到打开前的状态
const popupClose = () => {
  restoreFilterCriteria()
}

// ===================== 筛选条件状态管理 =======================
// 恢复筛选条件：将筛选条件恢复到备份状态
const restoreFilterCriteria = () => {
  const restored = {
    isStock: backupFilterCriteria.value.isStock,
    minPrice: backupFilterCriteria.value.minPrice,
    maxPrice: backupFilterCriteria.value.maxPrice,
    // 恢复品牌选中状态到备份时的状态
    brandsList: filterCriteria.value.brandsList.map((brand, index) => ({
      ...brand,
      isSelected: backupFilterCriteria.value.brandsList[index]?.isSelected || false
    }))
  }
  emit('update:modelValue', restored)
}

// 备份当前筛选条件：保存当前状态用于后续恢复
const backupCurrentFilterCriteria = () => {
  backupFilterCriteria.value = {
    isStock: filterCriteria.value.isStock,
    minPrice: filterCriteria.value.minPrice,
    maxPrice: filterCriteria.value.maxPrice,
    // 深拷贝品牌列表数据
    brandsList: JSON.parse(JSON.stringify(filterCriteria.value.brandsList))
  }
}

// 设置地址切换弹窗显示状态
const setSwitchAddressPopupShow = () => {
  emit('switch-address')
}

// ===================== 筛选条件变更事件处理 =======================
// 切换仅看有货状态：在有货和全部之间切换
const onIsStock = () => {
  const newFilterCriteria = { ...filterCriteria.value }
  // 切换库存状态：true表示仅看有货，''||false表示全部
  newFilterCriteria.isStock = newFilterCriteria.isStock === true
  emit('update:modelValue', newFilterCriteria)
}

// 价格输入失焦事件：验证并调整价格范围
const onFilterCriteriaPriceBlur = () => {
  const newFilterCriteria = { ...filterCriteria.value }
  // 如果最小价格大于最大价格，则交换两个值
  if (newFilterCriteria.minPrice && newFilterCriteria.maxPrice) {
    if (Number(newFilterCriteria.minPrice) > Number(newFilterCriteria.maxPrice)) {
      const temp = newFilterCriteria.minPrice
      newFilterCriteria.minPrice = newFilterCriteria.maxPrice
      newFilterCriteria.maxPrice = temp
    }
  }
  emit('update:modelValue', newFilterCriteria)
}

// 品牌选择：切换品牌选中状态（兼容旧版本调用方式）
const onBrandSelect = (brand) => {
  // 切换品牌选中状态
  brand.isSelected = !brand.isSelected
  emit('update:modelValue', filterCriteria.value)
}

// ===================== 筛选操作事件处理 =======================
// 重置筛选条件：清空所有筛选条件并备份
const resetFilter = () => {
  const newFilterCriteria = {
    isStock: '',
    minPrice: '',
    maxPrice: '',
    // 将所有品牌设置为未选中状态
    brandsList: filterCriteria.value.brandsList.map(brand => ({
      ...brand,
      isSelected: false
    }))
  }
  emit('update:modelValue', newFilterCriteria)
  // 备份重置后的状态
  backupCurrentFilterCriteria()
}

// 确认筛选：备份当前筛选条件并触发确认事件
const confirmFilter = () => {
  // 备份当前筛选条件
  backupCurrentFilterCriteria()
  // 触发确认事件，通知父组件应用筛选
  emit('confirm')
}

// 重置筛选条件按钮点击事件：调用重置筛选方法
const filterResetBtnClick = () => {
  resetFilter()
}

// 确认筛选按钮点击事件：调用确认筛选方法
const filterConfirmBtnClick = () => {
  confirmFilter()
}
</script>

<style scoped lang="less">
.filter-area {
  position: relative;
  height: 80vh;
  margin-top: 50px;
  padding: 0 20px 0 20px;
  overflow: scroll;

  .filter-area-content {
    .filter-area-base {
      margin-bottom: 20px;

      .title {
        margin-bottom: 10px;
        font-size: 15px;
        color: #171E24;
        line-height: 15px;
        font-weight: 500;
      }

      .price-range {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .input-number {
          background: #F8F9FA;
          border-radius: 4px;
          height: 25px;
          line-height: 25px;

          :deep(.van-field__control) {
            text-align: center !important;
            padding: 0 5px !important;
          }
        }

        :deep(.van-cell) {
          padding: 0;
        }

        .input-slicing {
          width: 20px;
          height: 2px !important;
          margin: 0 10px;
          background: rgba(177, 190, 201, 1);
        }
      }

      .filter-criteria {
        display: grid;
        grid-template-rows: repeat(3, 1fr);
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 10px;

        .condition {
          height: 25px;
          line-height: 25px;
          text-align: center;
          padding: 0 10px;
          font-size: 11px;
          color: #4A5568;
          font-weight: 400;
          background: #F8F9FA;
          border-radius: 4px;
          overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;

          &.active {
            color: #FFFFFF;
            background-image: var(--wo-biz-theme-gradient-1);
          }
        }
      }

      .filter-criteria-list {
        display: grid;
        grid-template-rows: repeat(1, 1fr);
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 10px;

        .condition {
          height: 25px;
          line-height: 25px;
          text-align: center;
          padding: 0 10px;
          font-size: 11px;
          color: #4A5568;
          font-weight: 400;
          background: #F8F9FA;
          border-radius: 4px;
          overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;

          &.active {
            color: #FFFFFF;
            background-image: var(--wo-biz-theme-gradient-1);
          }
        }
      }

      .filter-area-address {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        font-size: 13px;
        line-height: 1.5;
        color: #171E24;

        .filter-area-address-left {
          flex: 1;
          margin-right: 10px;
          align-items: center;
          overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;

          .filter-area-address-icon {
            width: 14px;
            height: 15px;
            vertical-align: middle;
          }

          .filter-area-address-title {
            margin-left: 5px;
            font-weight: 700;
          }
        }

        .filter-area-address-right {
          .filter-area-address-op {
            color: var(--wo-biz-theme-color);
            font-weight: 500;
          }
        }
      }
    }
  }
}

.filter-area-operator {
  padding: 0 20px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  height: 65px;
  background: #FFFFFF;
  box-sizing: border-box;
}
</style>
